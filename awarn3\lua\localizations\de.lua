local language_code = "DE"

--[[German translation by Funny_TV]]--
--https://www.gmodstore.com/users/Funny_TV

 AWarn.Localization:RegisterLanguage( language_code, "German" )
 
AWarn.Localization:AddDefinition( language_code, "welcome1",                    "Willkomme<PERSON> zu AWarn3!" )
AWarn.Localization:AddDefinition( language_code, "insufficientperms",           "Unzureichende Rechte um dieses Command auszuführen." )
AWarn.Localization:AddDefinition( language_code, "insufficientperms2",          "Unzureichend Rechte um die Warns des Spielers einzusehen." )
AWarn.Localization:AddDefinition( language_code, "commandnonexist",             "Dieses Command existiert nicht." )
AWarn.Localization:AddDefinition( language_code, "invalidtargetid",             "Ungültige Person oder ID." )
AWarn.Localization:AddDefinition( language_code, "invalidtarget",               "Ungültige Person." )
AWarn.Localization:AddDefinition( language_code, "reasonrequired",              "Warn Grund benötigt." )
AWarn.Localization:AddDefinition( language_code, "remove1activewarn",           "Du entfernst 1x Warn von" )
AWarn.Localization:AddDefinition( language_code, "deletedwarningid",            "Warn ID gelöscht" )
AWarn.Localization:AddDefinition( language_code, "removeallwarnings",           "Du entfernst alle Warns von" )
AWarn.Localization:AddDefinition( language_code, "deletedwarningsfor",          "Alle Warns löschen für" )
AWarn.Localization:AddDefinition( language_code, "cantopenconsole",             "Du kannst diese Menü nicht über die Konsole öffnen." )
AWarn.Localization:AddDefinition( language_code, "invalidoption",               "Ungültige Option." )
AWarn.Localization:AddDefinition( language_code, "invalidoptionvaluetype",      "Ungültiger Wert." )
AWarn.Localization:AddDefinition( language_code, "optionsloaded",               "Option geladen!" )
AWarn.Localization:AddDefinition( language_code, "nopunishment",                "Keine Bestrafung für diese Warnung zählen." )
AWarn.Localization:AddDefinition( language_code, "punishmentsloaded",           "Bestrafungen geladen!" )
AWarn.Localization:AddDefinition( language_code, "playernotallowedwarn",        "Dieser Spieler kann nicht gewarnt werden." )
AWarn.Localization:AddDefinition( language_code, "warnmessage1",                "Du wurdest von %s für %s gewarnt." )
AWarn.Localization:AddDefinition( language_code, "warnmessage2",                "Du warnst %s für %s" )
AWarn.Localization:AddDefinition( language_code, "warnmessage3",                "%s wurde von %s für %s gewarnt" )
AWarn.Localization:AddDefinition( language_code, "warnmessage4",                "Du wurdest von %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage5",                "Du warnst %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage6",                "%s wurde von %s." )
AWarn.Localization:AddDefinition( language_code, "joinmessage1",                "joint dem Server mit Warns." )
AWarn.Localization:AddDefinition( language_code, "joinmessage2",                "Der letzte Warn ist vom:" )
AWarn.Localization:AddDefinition( language_code, "joinmessage3",                "Willkommen zurück! Du wurdest in der Vergangenheit schon einmal gewarnt." )
AWarn.Localization:AddDefinition( language_code, "joinmessage4",                "Du kannst deine Warns mit folgendem Command einsehen:" )
AWarn.Localization:AddDefinition( language_code, "closemenu",                   "Menuü schliessen" )
AWarn.Localization:AddDefinition( language_code, "searchplayers",               "Spieler suchen" )
AWarn.Localization:AddDefinition( language_code, "viewwarnings",                "Warns Anzeigen" )
AWarn.Localization:AddDefinition( language_code, "configuration",               "Konfiguration" )
AWarn.Localization:AddDefinition( language_code, "clientoptions",               "User Optionen" )
AWarn.Localization:AddDefinition( language_code, "serveroptions",               "Server Optionen" )
AWarn.Localization:AddDefinition( language_code, "colorcustomization",          "Farb Einstellungen" )
AWarn.Localization:AddDefinition( language_code, "colorselection",              "Farb auswahl" )
AWarn.Localization:AddDefinition( language_code, "languageconfiguration",       "Sprach Einstellungen" )
AWarn.Localization:AddDefinition( language_code, "selectlanguage",              "Sprache auswählen" )
AWarn.Localization:AddDefinition( language_code, "enablekickpunish",            "Kick Bestrafung Aktivieren" )
AWarn.Localization:AddDefinition( language_code, "enablebanpunish",             "Ban Bestrafung Aktivieren" )
AWarn.Localization:AddDefinition( language_code, "enabledecay",                 "Warn verfall aktivieren" )
AWarn.Localization:AddDefinition( language_code, "resetafterban",               "Aktive Warn nach Ban reseten" )
AWarn.Localization:AddDefinition( language_code, "allowwarnadmins",             "Erlaube Teammitglieder zu warnen" )
AWarn.Localization:AddDefinition( language_code, "pressenter",                  "Enter zum Speichern" )
AWarn.Localization:AddDefinition( language_code, "entertosave",                 "Enter zum Speichern" )
AWarn.Localization:AddDefinition( language_code, "chatprefix",                  "Chat Prefix" )
AWarn.Localization:AddDefinition( language_code, "warningdecayrate",            "Warn verfall Rate (in Minuten)" )
AWarn.Localization:AddDefinition( language_code, "serverlanguage",              "Server Sprache" )
AWarn.Localization:AddDefinition( language_code, "punishmentsconfiguration",    "Bestrafungs Einstellungen" )
AWarn.Localization:AddDefinition( language_code, "addpunishment",               "Bestrafung hinzufügen" )
AWarn.Localization:AddDefinition( language_code, "warnings",                    "Warnungen" )
AWarn.Localization:AddDefinition( language_code, "punishtype",                  "Bestrafungs Typ" )
AWarn.Localization:AddDefinition( language_code, "punishlength",                "Bestrafungs Länge" )
AWarn.Localization:AddDefinition( language_code, "playermessage",               "Spieler Nachricht" )
AWarn.Localization:AddDefinition( language_code, "playername",                  "Spieler Name" )
AWarn.Localization:AddDefinition( language_code, "messagetoplayer",             "Nachrivht an Spieler" )
AWarn.Localization:AddDefinition( language_code, "servermessage",               "Server Nachricht" )
AWarn.Localization:AddDefinition( language_code, "messagetoserver",             "Nachricht an Server" )
AWarn.Localization:AddDefinition( language_code, "deletewarning",               "Warn löschen" )
AWarn.Localization:AddDefinition( language_code, "punishaddmenu",               "Bestrafung hinzufügen Menü" )
AWarn.Localization:AddDefinition( language_code, "inminutes",                   "In Minuten" )
AWarn.Localization:AddDefinition( language_code, "0equalperma",                 "0 = Permanent" )
AWarn.Localization:AddDefinition( language_code, "use%",                        "Benutz %s um den Namen des Spieler einzusehen" )
AWarn.Localization:AddDefinition( language_code, "setdefault",                  "Standart setzen" )
AWarn.Localization:AddDefinition( language_code, "showingownwarnings",          "Eigene Warnungen anzeigen" )
AWarn.Localization:AddDefinition( language_code, "warnedby",                    "Gewarnt von" )
AWarn.Localization:AddDefinition( language_code, "warningserver",               "Warn Server" )
AWarn.Localization:AddDefinition( language_code, "warningreason",               "Warn Grund" )
AWarn.Localization:AddDefinition( language_code, "warningdate",                 "Warn Datum" )
AWarn.Localization:AddDefinition( language_code, "nothing",                     "NICHTS" )
AWarn.Localization:AddDefinition( language_code, "submit",                      "Bestätigen" )
AWarn.Localization:AddDefinition( language_code, "connectedplayers",            "Verbundene Spieler" )
AWarn.Localization:AddDefinition( language_code, "displaywarningsfor",          "Warns anzeigen für" )
AWarn.Localization:AddDefinition( language_code, "activewarnings",              "Active Warns" )
AWarn.Localization:AddDefinition( language_code, "selectedplayernowarnings",    "Der Spieler hat keine aktiven Warns." )
AWarn.Localization:AddDefinition( language_code, "selectplayerseewarnings",     "Spieler aussuchen um Warns einzusehen." )
AWarn.Localization:AddDefinition( language_code, "warnplayer",                  "Spieler Warnen" )
AWarn.Localization:AddDefinition( language_code, "reduceactiveby1",             "Reduzier aktive Warns um 1" )
AWarn.Localization:AddDefinition( language_code, "playerwarningmenu",           "Spieler Warn Menü" )
AWarn.Localization:AddDefinition( language_code, "playersearchmenu",            "Spieler such Menü" )
AWarn.Localization:AddDefinition( language_code, "warningplayer",               "Warns Spieler" )
AWarn.Localization:AddDefinition( language_code, "excludeplayers",              "Spieler mit 0 Warns im Verlauf Ausschließen" )
AWarn.Localization:AddDefinition( language_code, "searchforplayers",            "Spiler anhand von Namen oder SteamID64 suchen" )
AWarn.Localization:AddDefinition( language_code, "name",                        "Name" )
AWarn.Localization:AddDefinition( language_code, "lastplayed",                  "Als letztes online am" )
AWarn.Localization:AddDefinition( language_code, "lastwarned",                  "Als letztes gewarnt am" )
AWarn.Localization:AddDefinition( language_code, "never",                       "Noch nie" )
AWarn.Localization:AddDefinition( language_code, "playerid",                    "Spieler ID" )
AWarn.Localization:AddDefinition( language_code, "lookupplayerwarnings",        "Spieler's Warns anzeigen" )
AWarn.Localization:AddDefinition( language_code, "servername",                  "Server Name" )
AWarn.Localization:AddDefinition( language_code, "clientjoinmessage",			"Warnungszähler für Spieler beim Beitritt anzeigen" )
AWarn.Localization:AddDefinition( language_code, "adminjoinmessage",			"Admins eine Nachricht anzeigen, wenn Spieler mit Warnungen beitreten" )
AWarn.Localization:AddDefinition( language_code, "punishmentoptions",			"Strafen" )
AWarn.Localization:AddDefinition( language_code, "kickpunishdescription",		"Wenn aktiviert, kann AWarn3 Spieler als Strafe vom Server werfen." )
AWarn.Localization:AddDefinition( language_code, "banpunishdescription",		"Wenn aktiviert, kann AWarn3 Spieler als Strafe vom Server sperren." )
AWarn.Localization:AddDefinition( language_code, "enabledecaydescription",		"Wenn aktiviert, werden aktive Warnungen mit der Zeit abklingen." )
AWarn.Localization:AddDefinition( language_code, "reasonrequireddescription",	"Wenn diese Option aktiviert ist, müssen Administratoren in ihrer Warnung einen Grund angeben." )
AWarn.Localization:AddDefinition( language_code, "resetafterbandescription",	"Wenn aktiviert, werden die aktiven Warnungen eines Benutzers auf 0 zurückgesetzt, nachdem sie von AWarn3 gesperrt wurden." )
AWarn.Localization:AddDefinition( language_code, "logevents",					"Warnereignisse protokollieren." )
AWarn.Localization:AddDefinition( language_code, "logeventsdescription",		"Wenn aktiviert, werden Aktionen in AWarn3 in einer Textdatei protokolliert." )
AWarn.Localization:AddDefinition( language_code, "allowwarnadminsdescription",	"Wenn diese Option aktiviert ist, können Administratoren andere Administratoren warnen." )
AWarn.Localization:AddDefinition( language_code, "clientjoinmessagedescription","Wenn diese Option aktiviert ist, sehen Benutzer, die dem Server beitreten, eine Nachricht im Chat, wenn sie Warnungen haben." )
AWarn.Localization:AddDefinition( language_code, "adminjoinmessagedescription",	"Wenn diese Option aktiviert ist, sehen Admins auf dem Server, wenn ein Spieler beitritt, der Warnungen hat." )
AWarn.Localization:AddDefinition( language_code, "chatprefixdescription",		"Der Chat-Befehl, der für AWarn3-Befehle verwendet wird. Standard: !warn" )
AWarn.Localization:AddDefinition( language_code, "warningdecayratedescription",	"The time (in minutes) a player needs to be connected for 1 active warning to decay." )
AWarn.Localization:AddDefinition( language_code, "servernamedescription",		"Die Zeit (in Minuten), die ein Spieler benötigt, damit eine aktive Warnung abklingt." )
AWarn.Localization:AddDefinition( language_code, "selectlanguagedescription",	"Dies ist die Sprache, in der Servernachrichten angezeigt werden." )
AWarn.Localization:AddDefinition( language_code, "theme",						"Interface-Theme" )
AWarn.Localization:AddDefinition( language_code, "themeselect",					"Thema wählen" )
AWarn.Localization:AddDefinition( language_code, "punishgroup",					"Bestrafungsgruppe" )
AWarn.Localization:AddDefinition( language_code, "grouptoset",					"Zum Einstellen gruppieren" )
AWarn.Localization:AddDefinition( language_code, "viewnotes",					"Spielernotizen anzeigen" )
AWarn.Localization:AddDefinition( language_code, "playernotes",					"Spielernotizen" )
AWarn.Localization:AddDefinition( language_code, "interfacecustomizations",		"Schnittstellenanpassungen" )
AWarn.Localization:AddDefinition( language_code, "enableblur",					"Hintergrundunschärfe aktivieren" )
AWarn.Localization:AddDefinition( language_code, "chooseapreset",				"Wählen Sie eine Voreinstellung (optional)" )
AWarn.Localization:AddDefinition( language_code, "warningpresets",				"Voreinstellungen" )
AWarn.Localization:AddDefinition( language_code, "addeditpreset",				"Voreinstellung hinzufügen/bearbeiten" )
AWarn.Localization:AddDefinition( language_code, "presetname",					"Voreinstellungsname" )
AWarn.Localization:AddDefinition( language_code, "presetreason",				"Voreingestellter Grund" )
