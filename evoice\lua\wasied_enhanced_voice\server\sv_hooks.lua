-- Override the default voice chat gamemode functions to make it use EVoice system (more optimized)
hook.Add("PostGamemodeLoaded", "EVoice:PostGamemodeLoaded", function()

	GM = GM or GAMEMODE

	EVoice.tPlayerCanHear = EVoice.tPlayerCanHear or {}
	EVoice.fcOldPlayerCanHearPlayersVoice = GM.PlayerCanHearPlayersVoice

	function GM:PlayerCanHearPlayersVoice(pListener, pTalker)
		return EVoice:PlayerCanHearPlayersVoice(pListener, pTalker)
	end

	print("[EVoice] Gamemode function successfully overrided!")

end)

-- Set the player voice mode at spawn
hook.Add("PlayerInitialSpawn", "EVoice:PlayerInitialSpawn", function(pPlayer)
	if not IsValid(pPlayer) then return end
	pPlayer:SetVoiceMode(math.Clamp(EVoice.Config.DefaultVoiceMode or 2, 1, #EVoice.Config.Modes), true)
end)

-- Start the distance-between-players calculation timer at first tick
hook.Add("Tick", "EVoice:Tick", function()

	EVoice.tPlayerCanHear = EVoice.tPlayerCanHear or {}

	timer.Create("EVoice:CalculateDistance", EVoice.Constants["config"]["calculationCooldown"], 0, function()
		ProtectedCall(function()

			-- Some profilers will tell you that this function is not optimized. Please note that voice calculations are ALWAYS expensive.
			-- Here, we're doing the calculations only 2 times per second (by default) - so a "4ms" run time would actually mean only ~0.008ms/tick.
			-- If you want this to be 100% optimized, disable PreventHearingThroughWalls (set it to false) for every voice modes.

			local tPlayers = player.GetAll()
			local tValidPlayers = {}
			local tDistanceCache = {}
			local tCanHear = {}

			-- Remove all invalid players (to avoid checking them later)
			for _, p in ipairs(tPlayers) do

				if IsValid(p) then
					tValidPlayers[#tValidPlayers + 1] = p
				end

			end

			-- Loop through all listeners
			for _, pListener in ipairs(tValidPlayers) do

				local tListenerCache = {}

				-- Check if listener is in the range of any other player
				for _, pTalker in ipairs(tValidPlayers) do

					if pTalker == pListener then continue end

					if pTalker:Alive() then

						local iDistSqr = EVoice:CalculateDistance(pTalker, pListener, tDistanceCache)
						local iTalkerRange = pTalker:GetVoiceRange()

						if EVoice.Config.EnableMegaphone and pTalker.bIsMegaphoneTalking then
							iTalkerRange = EVoice.Constants["config"]["megaphoneRange"]
						end

						local bCloseEnoughToHear = iDistSqr <= iTalkerRange

						if bCloseEnoughToHear and pTalker:ShouldPreventVoiceThroughWalls() then
							bCloseEnoughToHear = not EVoice:IsVoiceBlockedByLOS(pTalker, pListener, tDistanceCache)
						end

						tListenerCache[pTalker] = bCloseEnoughToHear

					else
						tListenerCache[pTalker] = false
					end

				end

				tCanHear[pListener] = tListenerCache

			end

			EVoice.tPlayerCanHear = tCanHear

		end)
	end)

	hook.Remove("Tick", "EVoice:Tick")

end)

-- Change the voice mode
hook.Add("PlayerButtonDown", "EVoice:PlayerButtonDown", function(pPlayer, iKey)

	if not IsValid(pPlayer) or not pPlayer:Alive() then return end

	if (pPlayer.iVoiceKeyCooldown or 0) > CurTime() then return end
	pPlayer.iVoiceKeyCooldown = CurTime() + 0.2
	
	--local eWeapon = pPlayer:GetActiveWeapon()
	
	--if (IsValid(eWeapon) and eWeapon:GetClass() == "atg_fightmode") then return end

	if iKey == EVoice.Config.VoiceKey and not pPlayer.bIsMegaphoneTalking then
		pPlayer:ChangeVoiceMode()
	end

end)

-- Sync sound disabling with mic disabling
hook.Add("OnLocalNWVarChanged", "EVoice:OnLocalNWVarChanged", function(pPlayer, sVarName, xValue)

	if not EVoice.Config.EnableRadio then return end
	if not IsValid(pPlayer) or not pPlayer:Alive() then return end

	if sVarName == "RadioSoundEnabled" then

		if xValue == false then

			pPlayer.bOldRadioMicState = pPlayer:GetRadioMic()
			pPlayer.bSkipFirstNWChange = true

			pPlayer:SetRadioMic(false)

		elseif isbool(pPlayer.bOldRadioMicState) then
			pPlayer:SetRadioMic(pPlayer.bOldRadioMicState)
		end

	elseif sVarName == "RadioMicEnabled" then

		if not pPlayer.bSkipFirstNWChange then
			pPlayer.bOldRadioMicState = nil
		else
			pPlayer.bSkipFirstNWChange = nil
		end

	end

end)

if EVoice.Config.EnableRadio then

	-- Reset the radio when a player dies
	hook.Add("PostPlayerDeath", "EVoice:PostPlayerDeath", function(pPlayer)
		pPlayer:SetRadioEnabled(false)
		pPlayer:SetRadioFrequency(1)
		pPlayer:SetRadioSound(false)
		pPlayer:SetRadioMic(false)
	end)


	-- Reset the radio when a player respawns
	hook.Add("PlayerLoadout", "EVoice:PlayerLoadout", function(pPlayer)
		pPlayer:SetRadioEnabled(false)
		pPlayer:SetRadioFrequency(1)
		pPlayer:SetRadioSound(true)
		pPlayer:SetRadioMic(true)
	end)

	-- In case there's nothing installed
	local function CanHear(pListener, pTalker)

		local bBothRadioEnabled = pListener:GetRadioEnabled() and pTalker:GetRadioEnabled()
		local bSameFrequency = pListener:GetRadioFrequency() == pTalker:GetRadioFrequency()

		if bBothRadioEnabled and bSameFrequency and pTalker:GetRadioMic() and pListener:GetRadioSound() then
			return true
		end

	end
	hook.Add("PlayerCanHearPlayersVoice", "EVoice:Radio:PlayerCanHearPlayersVoice", CanHear)

	-- In case there's VoiceBox installed
	local function VoiceboxCanHear(pListener, pTalker)

		local bBothRadioEnabled = pListener:GetRadioEnabled() and pTalker:GetRadioEnabled()
		local bSameFrequency = pListener:GetRadioFrequency() == pTalker:GetRadioFrequency()

		if bBothRadioEnabled and bSameFrequency and pTalker:GetRadioMic() and pListener:GetRadioSound() then
			VoiceBox.FX.IsRadioComm(pListener:EntIndex(), pTalker:EntIndex(), not VoiceBox.FX.__PlayerCanHearPlayersVoice(pListener, pTalker))
			return true
		end

		VoiceBox.FX.IsRadioComm(pListener:EntIndex(), pTalker:EntIndex(), false)

	end

	if VoiceBox and VoiceBox.FX then
		hook.Add("PlayerCanHearPlayersVoice", "EVoice:Radio:PlayerCanHearPlayersVoice", VoiceboxCanHear)
	else
		hook.Add("VoiceBox.FX", "EVoice:Radio:VoiceBox.FX", function()
			hook.Add("PlayerCanHearPlayersVoice", "EVoice:Radio:PlayerCanHearPlayersVoice", VoiceboxCanHear)
		end)
	end

end