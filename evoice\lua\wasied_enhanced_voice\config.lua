EVoice.Config = {}

--[[ WARNING ]]--
--[[ This an advanced configuration. The script will work without you editing anything here. ]]--
--[[ Don't try to do anything you don't understand in the configuration file. ]]--
--[[ WARNING ]]--

-- Key used by players to change their voice mode
-- See more here: https://wiki.facepunch.com/gmod/Enums/KEY
EVoice.Config.VoiceKey = KEY_H

-- Voices modes configuration
-- You can add/remove as many modes as you want
EVoice.Config.Modes = {

	{ -- Whisper mode
		ModeName = "Chuchotement",
		ModeColor = Color(46, 204, 113),
		HearingDistance = 75,
		PreventHearingThroughWalls = true -- With this setting enabled, players won't hear whispers through walls. Can be laggy on some servers if enabled.
	},
	
	{ -- Normal mode
		ModeName = "Normal",
		ModeColor = Color(52, 152, 219),
		HearingDistance = 250,
		PreventHearingThroughWalls = true
	},
	
	{ -- Yell mode
		ModeName = "Hurlement",
		ModeColor = Color(231, 76, 60),
		HearingDistance = 600,
		PreventHearingThroughWalls = false
	},

}

-- The default voice mod when the player joins the server
-- Provide the index of the mode in the table above (1 for whisper, 2 for normal, 3 for yell, etc...)
EVoice.Config.DefaultVoiceMode = 2

-- Restricted frequencies for jobs
-- Example: Frequency 199 is restricted to Police jobs
EVoice.Config.RestrictedFrequencies = {
	[199] = { 
		sName = "Police",
		tJobs = { -- Put your jobs names here
			"Police Officer",
			"Police Protection Chief"
		},
	},
	[198] = {
		sName = "Médecins",
		tJobs = {
			"EMS",
		},
	},
	[197] = {
		sName = "Gouvernement",
		tJobs = {
			"Civil Protection",
			"Civil Protection Chief",
			"EMS",
			"Mayor",
		},
	},
	[196] = { 
		sName = "Urgences 911",
		tJobs = {
			"Police Officer",
			"Police Officer Chief",
			"EMS",
		},
	},
	[195] = {
		sName = "Gardes du maire",
		tJobs = {
			"Mayor",
			"Mayor Guard",
		},
	},
	[194] = {
		sName = "Bloodz",
		tJobs = {
			"Bloodz",
			"Bloodz Chief",
		},
	},
	[193] = {
		sName = "Cripz",
		tJobs = {
			"Cripz",
			"Cripz Chief",
		},
	},
}

-- Duration of the circle animation while switching mode
EVoice.Config.AnimDuration = 4

-- Enable the circle animation for the megaphone (this can be ugly on some maps)
EVoice.Config.MegaphoneAnim = true

-- Enable the megaphone system
EVoice.Config.EnableMegaphone = false

-- Enable the radio system (requires a restart)
EVoice.Config.EnableRadio = false

-- Language configuration
EVoice.Config.Language = {
	[1] = "Son",
	[2] = "Microphone",
	[3] = "Fréquence",
	[4] = "Clic molette",
	[5] = "Fréquence radio",
	[6] = "Choisissez une fréquence radio",
	[7] = "Confirmer",
	[8] = "Désactiver la radio",
	[9] = "Vous êtes maintenant en mode",
	[10] = "Vous devez utiliser une fréquence entre %i et %i !",
	[11] = "Fermer le menu",
	[12] = "Vous n'avez pas accès à ces fréquences restreintes !",
	[13] = "Clic molette pour activer"
}