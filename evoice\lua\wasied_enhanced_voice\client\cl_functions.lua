EVoice.Fonts = {}

-- Automatic responsive functions
RX = RX or function(x) return x / 1920 * ScrW() end
RY = RY or function(y) return y / 1080 * ScrH() end

-- Automatic font-creation function
function EVoice:Font(iSize, sType)

	iSize = iSize or 15
	sType = sType or "Medium"

	local sName = ("EVoice:Font:%i:%s"):format(iSize, sType)
	if not EVoice.Fonts[sName] then

		surface.CreateFont(sName, {
			font = ("MontserratWasied %s"):format(sType):Trim(),
			size = RX(iSize),
			width = 500,
			extended = false
		})

		EVoice.Fonts[sName] = true

	end

	return sName

end

-- Create the radio channel interface
function EVoice:CreateChannel()

	if not EVoice.Config.EnableRadio then return end

	if IsValid(self.vRadioFrame) then
		self.vRadioFrame:Remove()
	end

	local vFrame = vgui.Create("DFrame")
	vFrame:SetSize(RX(600), RY(235))
	vFrame:Center()
	vFrame:SetTitle("")
	vFrame:MakePopup()
	vFrame:ShowCloseButton(false)
	vFrame:SetDraggable(false)
	function vFrame:Paint(w, h)

		draw.RoundedBox(8, 0, 0, w, h, EVoice.Constants["colors"]["frameBackground"])

	end
	self.vRadioFrame = vFrame

	local tLerps = {}

	local vLeft = vgui.Create("DPanel", vFrame)
	vLeft:SetSize(vFrame:GetWide() / 2 - RX(50), vFrame:GetTall())
	function vLeft:Paint(w, h)
		
		draw.RoundedBoxEx(8, 0, 0, w, h, EVoice.Constants["colors"]["contentBackground"], true, false, true, false)

		draw.SimpleText("Fréquences réservées", EVoice:Font(23, "SemiBold"), w / 2, RY(20), color_white, 1)
		
		surface.SetDrawColor(ColorAlpha(color_white, 5))
		surface.DrawLine(RX(50), RY(52), w - RX(50), RY(52))

		local tFrequencies = {}
		for iFrequency, tConfig in pairs(EVoice.Config.RestrictedFrequencies) do
			table.insert(tFrequencies, {
				iFrequency = iFrequency,
				sName = tConfig.sName
			})
		end

		table.SortByMember(tFrequencies, "iFrequency", true)

		local tShownFrequencies = {}
		local bScrolling = #tFrequencies > 6
		
		if not bScrolling then
			tShownFrequencies = tFrequencies
		else
			local iOffset = math.Round(CurTime() * 0.5) % table.Count(EVoice.Config.RestrictedFrequencies)

			for i = iOffset, iOffset + 5 do
				local iIdx = (i % #tFrequencies) + 1
				table.insert(tShownFrequencies, tFrequencies[iIdx])
			end

			for _, tFrequency in ipairs(tFrequencies) do
				if not table.HasValue(tShownFrequencies, tFrequency) then
					tLerps[tFrequency.iFrequency] = nil
				end
			end
		end

		local i = 0
		for _, tFrequency in ipairs(tShownFrequencies) do

			local iY = RY(65 + i * 25)
			local iDistFromCenter = math.abs(h / 2 - iY)
			local iAlpha = bScrolling and math.Remap(iDistFromCenter, 0, h / 2, 255, -50) or 255

			tLerps[tFrequency.iFrequency] = Lerp(FrameTime() * 10, tLerps[tFrequency.iFrequency] or h, iY)

			draw.SimpleText(("%s - %s"):format(tFrequency.iFrequency, tFrequency.sName), EVoice:Font(23, "Italic"), RX(25), tLerps[tFrequency.iFrequency], ColorAlpha(EVoice.Constants["colors"]["lightGray"], iAlpha))
			i = i + 1
		
		end

	end

	local vRight = vgui.Create("DPanel", vFrame)
	vRight:SetSize(vFrame:GetWide() / 2, vFrame:GetTall())
	vRight:SetX(vFrame:GetWide() / 2 - RX(25))
	function vRight:Paint(w, h)

		draw.SimpleText(EVoice.Config.Language[5], EVoice:Font(40, "SemiBold"), w / 2, RY(40), color_white, 1, 1)
		draw.SimpleText(EVoice.Config.Language[6], EVoice:Font(22), w / 2, RY(70), EVoice.Constants["colors"]["lightGray"], 1, 1)

	end

	local vMiddlePanel = vgui.Create("DPanel", vRight)
	vMiddlePanel:SetSize(vFrame:GetWide(), RY(45))
	vMiddlePanel:SetY(RY(100))
	vMiddlePanel:CenterHorizontal()
	vMiddlePanel.Paint = nil

	local vTextPanel = vgui.Create("DPanel", vMiddlePanel)
	vTextPanel:Dock(LEFT)
	vTextPanel:SetWide(RX(100))
	function vTextPanel:Paint(w, h)
		draw.RoundedBox(4, 0, 0, w, h, EVoice.Constants["colors"]["textEntryBackground"])
	end

	local vText = vgui.Create("DTextEntry", vTextPanel)
	vText:Dock(FILL)
	vText:DockMargin(RX(28), 0, RX(25), 0)
	vText:SetDrawLanguageID(false)
	vText:SetPaintBackground(false)
	vText:SetFont(EVoice:Font(25))
	vText:SetTextColor(color_white)
	vText:SetPlaceholderText("127")
	vText:SetNumeric(true)
	vText:SetCursorColor(color_white)
	function vText:AllowInput(sInput)
		return #self:GetValue() >= 3
	end
	function vText:Paint(w, h)
		
		local iWide = draw.SimpleText(self:GetText(), EVoice:Font(29, "SemiBold"), w / 2, h / 2, color_white, 1, 1)

		if self:HasFocus() then

			local iAlpha = math.Round(CurTime() * 2) % 2 == 0 and 255 or 0
			draw.SimpleText("|", EVoice:Font(29), w / 2 + iWide / 2 + RX(2), h / 2 - RY(1), ColorAlpha(color_white, iAlpha), 0, 1)

		end

	end
	
	local iFrequency = LocalPlayer():GetLocalNWVar("RadioFrequency")
	if iFrequency then
		vText:SetText(tostring(iFrequency))
	end

	local vConfirm = vgui.Create("DButton", vMiddlePanel)
	vConfirm:Dock(LEFT)
	vConfirm:DockMargin(RX(10), 0, 0, 0)
	vConfirm:SetWide(RX(150))
	vConfirm:SetText("")
	function vConfirm:Paint(w, h)
		draw.RoundedBox(4, 0, 0, w, h, EVoice.Constants["colors"]["confirm"])
		draw.SimpleText(EVoice.Config.Language[7], EVoice:Font(25), w / 2, h / 2, color_white, 1, 1)
	end
	function vConfirm:DoClick()

		local iFrequency = tonumber(vText:GetValue())
		
		if isnumber(iFrequency) then
			
			net.Start("EVoice:UpdateLocalVar")
				net.WriteUInt(1, 4)
				net.WriteUInt(iFrequency, 32)
			net.SendToServer()

		end
			
		vFrame:Remove()

	end

	vMiddlePanel:SetWide(vTextPanel:GetWide() + vConfirm:GetWide() + vConfirm:GetDockMargin())
	vMiddlePanel:CenterHorizontal()

	local vDisable = vgui.Create("DButton", vRight)
	vDisable:SetSize(vMiddlePanel:GetWide(), vMiddlePanel:GetTall())
	vDisable:SetY(vMiddlePanel:GetY() + vMiddlePanel:GetTall() + RY(10))
	vDisable:CenterHorizontal()
	vDisable:SetText("")
	if not LocalPlayer():GetRadioEnabled() then
		vDisable.bDisabled = true
	end
	function vDisable:Paint(w, h)
		draw.RoundedBox(4, 0, 0, w, h, EVoice.Constants["colors"]["danger"])
		draw.SimpleText((vDisable.bDisabled and EVoice.Config.Language[11] or EVoice.Config.Language[8]), EVoice:Font(25), w / 2, h / 2, color_white, 1, 1)
	end
	function vDisable:DoClick()

		if not vDisable.bDisabled then
			
			net.Start("EVoice:UpdateLocalVar")
				net.WriteUInt(0, 4)
				net.WriteBool(false)
			net.SendToServer()

		end

		vFrame:Remove()

	end

end