local language_code = "NO"

AWarn.Localization:RegisterLanguage( language_code, "Norwegian" )

AWarn.Localization:AddDefinition( language_code, "welcome1", 					"Velkommen til AWarn3!" )
AWarn.Localization:AddDefinition( language_code, "insufficientperms", 			"Du har ingen tilgang til denne kommandoen!" )
AWarn.Localization:AddDefinition( language_code, "insufficientperms2", 			"Du har ingen tilgang til og se denne profilen!" )
AWarn.Localization:AddDefinition( language_code, "commandnonexist", 			"Kommandoen eksisterer ikke." )
AWarn.Localization:AddDefinition( language_code, "invalidtargetid", 			"Ugyldig bruker eller ID." )
AWarn.Localization:AddDefinition( language_code, "invalidtarget", 				"Ugyldig bruker." )
AWarn.Localization:AddDefinition( language_code, "reasonrequired", 				"Du må ha en grunn til og gi straff" )
AWarn.Localization:AddDefinition( language_code, "remove1activewarn", 			"Du har fjernet 1 aktiv straff fra" )
AWarn.Localization:AddDefinition( language_code, "deletedwarningid", 			"Slettet straffs ID" )
AWarn.Localization:AddDefinition( language_code, "removeallwarnings", 			"Du har fjernet alle straffer fra" )
AWarn.Localization:AddDefinition( language_code, "deletedwarningsfor", 			"Slettet alle straffer for" )
AWarn.Localization:AddDefinition( language_code, "cantopenconsole", 			"Du kan ikke åpne menyen fra konsollen" )
AWarn.Localization:AddDefinition( language_code, "invalidoption", 				"Ugyldig instilling." )
AWarn.Localization:AddDefinition( language_code, "invalidoptionvaluetype",		"Ugyldig alternativstypetype." )
AWarn.Localization:AddDefinition( language_code, "optionsloaded",				"Instilling lastet!" )
AWarn.Localization:AddDefinition( language_code, "nopunishment",				"Ingen straff for denne advarselen teller." )
AWarn.Localization:AddDefinition( language_code, "punishmentsloaded",			"Advarsel lastet!" )
AWarn.Localization:AddDefinition( language_code, "playernotallowedwarn",		"Denne spilleren kan ikke blir straffet" )
AWarn.Localization:AddDefinition( language_code, "warnmessage1",				"Du har motatt en straff fra %s for %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage2",				"Du straffet %s for %s" )
AWarn.Localization:AddDefinition( language_code, "warnmessage3",				"%s var straffet av %s for %s" )
AWarn.Localization:AddDefinition( language_code, "warnmessage1",				"Du har motatt en straff fra %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage2",				"Du straffet %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage3",				"%s var straffet av %s." )
AWarn.Localization:AddDefinition( language_code, "joinmessage1",				"har joinet serveren med straffer." )
AWarn.Localization:AddDefinition( language_code, "joinmessage2",				"Siste motatt straff var:" )
AWarn.Localization:AddDefinition( language_code, "joinmessage3",				"Velkommen tilbake til serveren. Det ser ut som at du har blitt straffet tidligere" )
AWarn.Localization:AddDefinition( language_code, "joinmessage4",				"Du kan se dinne straffer ved og skrive" )
AWarn.Localization:AddDefinition( language_code, "closemenu",					"Lukk Meny" )
AWarn.Localization:AddDefinition( language_code, "searchplayers",				"Søk for spillere" )
AWarn.Localization:AddDefinition( language_code, "viewwarnings",				"Se straffer" )
AWarn.Localization:AddDefinition( language_code, "configuration",				"Konfigurasjon" )
AWarn.Localization:AddDefinition( language_code, "clientoptions",				"Bruker instillinger" )
AWarn.Localization:AddDefinition( language_code, "serveroptions",				"Server instillinger" )
AWarn.Localization:AddDefinition( language_code, "colorcustomization",			"Farge Konfigurasjoner" )
AWarn.Localization:AddDefinition( language_code, "colorselection",				"Farge Alternativer" )
AWarn.Localization:AddDefinition( language_code, "languageconfiguration",		"Språk konfigurasjon" )
AWarn.Localization:AddDefinition( language_code, "selectlanguage",				"Velg et språk" )
AWarn.Localization:AddDefinition( language_code, "enablekickpunish",			"Aktiver Kick Straff" )
AWarn.Localization:AddDefinition( language_code, "enablebanpunish",				"Aktiver Ban Straff" )
AWarn.Localization:AddDefinition( language_code, "enabledecay",					"Aktiver Straffs Forfalling" )
AWarn.Localization:AddDefinition( language_code, "resetafterban",				"Tilbakestill Aktive Straffer Etter Ban" )
AWarn.Localization:AddDefinition( language_code, "allowwarnadmins",				"Tilatt at administratorer kan bli straffet" )
AWarn.Localization:AddDefinition( language_code, "pressenter",					"Trykk 'ENTER' for og lagre endringer" )
AWarn.Localization:AddDefinition( language_code, "entertosave",					"'ENTER' for lagring" )
AWarn.Localization:AddDefinition( language_code, "chatprefix",					"Chat Prefix" )
AWarn.Localization:AddDefinition( language_code, "warningdecayrate",			"Straff forfalls tid ( minutter )" )
AWarn.Localization:AddDefinition( language_code, "serverlanguage",				"Server Språk")
AWarn.Localization:AddDefinition( language_code, "punishmentsconfiguration",	"Straffs konfigurasjon" )
AWarn.Localization:AddDefinition( language_code, "addpunishment",				"Legg till straff" )
AWarn.Localization:AddDefinition( language_code, "warnings",					"Straffer" )
AWarn.Localization:AddDefinition( language_code, "punishtype",					"Straffs type" )
AWarn.Localization:AddDefinition( language_code, "punishlength",				"Straff lengde" )
AWarn.Localization:AddDefinition( language_code, "playermessage",				"Spiller melding" )
AWarn.Localization:AddDefinition( language_code, "playername",					"Spiller navn" )
AWarn.Localization:AddDefinition( language_code, "messagetoplayer",				"Melding Til Spiller" )
AWarn.Localization:AddDefinition( language_code, "servermessage",				"Server Melding" )
AWarn.Localization:AddDefinition( language_code, "messagetoserver",				"Melding til Server" )
AWarn.Localization:AddDefinition( language_code, "deletewarning",				"Slett alle Straffer" )
AWarn.Localization:AddDefinition( language_code, "punishaddmenu",				"Straff legg til meny" )
AWarn.Localization:AddDefinition( language_code, "inminutes",					"I Minutter" )
AWarn.Localization:AddDefinition( language_code, "0equalperma",					"0 = Permanent" )
AWarn.Localization:AddDefinition( language_code, "use%",						"Bruk %s for og vise spillerens navn" )
AWarn.Localization:AddDefinition( language_code, "setdefault",					"Sett standard" )
AWarn.Localization:AddDefinition( language_code, "showingownwarnings",			"Viser dine egne Straffer" )
AWarn.Localization:AddDefinition( language_code, "warnedby",					"Straffet" )
AWarn.Localization:AddDefinition( language_code, "warningserver",				"Straff Server" )
AWarn.Localization:AddDefinition( language_code, "warningreason",				"Straff Grunn" )
AWarn.Localization:AddDefinition( language_code, "warningdate",					"Straff Dato" )
AWarn.Localization:AddDefinition( language_code, "nothing",						"INGENTING" )
AWarn.Localization:AddDefinition( language_code, "submit",						"OK" )
AWarn.Localization:AddDefinition( language_code, "connectedplayers",			"Tilkoblede spillere" )
AWarn.Localization:AddDefinition( language_code, "displaywarningsfor",			"Viser Straffer For" )
AWarn.Localization:AddDefinition( language_code, "activewarnings",				"Aktive Straffer" )
AWarn.Localization:AddDefinition( language_code, "selectedplayernowarnings",	"Valgt Spiller Har Ingen Straffer." )
AWarn.Localization:AddDefinition( language_code, "selectplayerseewarnings",		"Velg en spiller for og se straffene dems." )
AWarn.Localization:AddDefinition( language_code, "warnplayer",					"Straff Player" )
AWarn.Localization:AddDefinition( language_code, "reduceactiveby1",				"Reduser aktive straffer med 1" )
AWarn.Localization:AddDefinition( language_code, "playerwarningmenu",			"Spiller Straff Meny" )
AWarn.Localization:AddDefinition( language_code, "playersearchmenu",			"Spiller Søk Meny" )
AWarn.Localization:AddDefinition( language_code, "warningplayer",				"Straff Spiller" )
AWarn.Localization:AddDefinition( language_code, "excludeplayers",				"Ekskluder spillere uten straffs historikk" )
AWarn.Localization:AddDefinition( language_code, "searchforplayers",			"Søk etter spiller med 'Navn' eller 'SteamID64'" )
AWarn.Localization:AddDefinition( language_code, "name",						"Navn" )
AWarn.Localization:AddDefinition( language_code, "lastplayed",					"Sist Spilt" )
AWarn.Localization:AddDefinition( language_code, "lastwarned",					"Sist Straffet" )
AWarn.Localization:AddDefinition( language_code, "never",						"Aldri" )
AWarn.Localization:AddDefinition( language_code, "playerid",					"Spiller ID" )
AWarn.Localization:AddDefinition( language_code, "lookupplayerwarnings",		"Slå opp denne spillerens straffer" )
AWarn.Localization:AddDefinition( language_code, "servername",					"Server Navn" )
AWarn.Localization:AddDefinition( language_code, "clientjoinmessage",			"Vis advarseltall for spilleren når han blir med" )
AWarn.Localization:AddDefinition( language_code, "adminjoinmessage",			"Vis melding til administratorer når spilleren slutter seg til advarsler" )
AWarn.Localization:AddDefinition( language_code, "punishmentoptions",			"Straffer" )
AWarn.Localization:AddDefinition( language_code, "kickpunishdescription",		"Hvis den er aktivert, kan AWarn3 sparke spillere fra serveren som en straff." )
AWarn.Localization:AddDefinition( language_code, "banpunishdescription",		"Hvis aktivert, kan AWarn3 utestenge spillere fra serveren som en straff." )
AWarn.Localization:AddDefinition( language_code, "enabledecaydescription",		"Hvis aktivert, vil aktive advarsler avta over tid." )
AWarn.Localization:AddDefinition( language_code, "reasonrequireddescription",	"Hvis aktivert, må administratorer oppgi en årsak i advarselen." )
AWarn.Localization:AddDefinition( language_code, "resetafterbandescription",	"Hvis aktivert, vil en brukers aktive advarsler tilbakestilles til 0 etter at de er utestengt av AWarn3." )
AWarn.Localization:AddDefinition( language_code, "logevents",					"Logg varslingshendelser." )
AWarn.Localization:AddDefinition( language_code, "logeventsdescription",		"Hvis aktivert, vil handlinger i AWarn3 logges til en tekstfil." )
AWarn.Localization:AddDefinition( language_code, "allowwarnadminsdescription",	"Hvis aktivert, vil administratorer kunne advare andre administratorer." )
AWarn.Localization:AddDefinition( language_code, "clientjoinmessagedescription","Hvis aktivert, vil brukere som blir med på serveren se en melding i chat hvis de har advarsler." )
AWarn.Localization:AddDefinition( language_code, "adminjoinmessagedescription",	"Hvis aktivert, vil administratorer på serveren se når en spiller blir med som har advarsler." )
AWarn.Localization:AddDefinition( language_code, "chatprefixdescription",		"Chat-kommandoen som brukes for AWarn3-kommandoer. Standard: !warn" )
AWarn.Localization:AddDefinition( language_code, "warningdecayratedescription",	"Tiden (i minutter) en spiller må være tilkoblet for at 1 aktiv advarsel skal forfalle." )
AWarn.Localization:AddDefinition( language_code, "servernamedescription",		"Navnet på denne serveren. Dette er nyttig for flere serveroppsett." )
AWarn.Localization:AddDefinition( language_code, "selectlanguagedescription",	"Dette er språket som servermeldinger vises på." )
AWarn.Localization:AddDefinition( language_code, "theme",						"Grensesnittstema" )
AWarn.Localization:AddDefinition( language_code, "themeselect",					"Velg tema" )
AWarn.Localization:AddDefinition( language_code, "punishgroup",					"Straffegruppe" )
AWarn.Localization:AddDefinition( language_code, "grouptoset",					"Gruppe å angi" )
AWarn.Localization:AddDefinition( language_code, "viewnotes",					"Se spillernotater" )
AWarn.Localization:AddDefinition( language_code, "playernotes",					"Spillernotater" )
AWarn.Localization:AddDefinition( language_code, "interfacecustomizations",		"Grensesnitttilpasninger" )
AWarn.Localization:AddDefinition( language_code, "enableblur",					"Aktiver bakgrunnsuskarphet" )
AWarn.Localization:AddDefinition( language_code, "chooseapreset",				"Velg en forhåndsinnstilling (valgfritt)" )
AWarn.Localization:AddDefinition( language_code, "warningpresets",				"Forhåndsinnstillinger" )
AWarn.Localization:AddDefinition( language_code, "addeditpreset",				"Legg til/rediger forhåndsinnstilling" )
AWarn.Localization:AddDefinition( language_code, "presetname",					"Forhåndsinnstilt navn" )
AWarn.Localization:AddDefinition( language_code, "presetreason",				"Forhåndsinnstilt grunn" )