-- Notify a player with the specified message
function EVoice:Notify(pPlayer, sContent)

	if not Is<PERSON>alid(pPlayer) or not pPlayer:IsPlayer() then return end

	if DarkRP then
		return DarkRP.notify(pPlayer, 0, 7, sContent)
	end

	return pPlayer:PrintMessage(HUD_PRINTTALK, sContent)
	
end

-- Check if 2 players can hear themselves
function EVoice:PlayerCanHearPlayersVoice(pListener, pTalker)
	
	local bCanHear = (self.tPlayerCanHear[pListener] or {})[pTalker]
	if not isbool(bCanHear) then return true, true end

	return bCanHear, bCanHear

end

-- Calculate the distance between 2 players with a cache system
function EVoice:CalculateDistance(pOne, pTwo, tCache)

	-- If we have the value cached, return it
	if istable(tCache) then

		if tCache[pOne] and tCache[pOne][pTwo] then
			return tCache[pOne][pTwo]
		end

		if tCache[pTwo] and tCache[pTwo][pOne] then
			return tCache[pTwo][pOne]
		end

	end

	-- Otherwise, calculate it and cache it for the next time
	local iDistSqr = pOne:GetPos():DistToSqr(pTwo:GetPos())
	
	if istable(tCache) then

		tCache[pOne] = tCache[pOne] or {}
		tCache[pOne][pTwo] = iDistSqr

		tCache[pTwo] = tCache[pTwo] or {}
		tCache[pTwo][pOne] = iDistSqr

	end

	return iDistSqr

end

-- Check if a wall is blocking the line of sight between 2 players
function EVoice:IsVoiceBlockedByLOS(pTalker, pListener, tDistanceCache)

	local tTrace = util.TraceLine({
		start = pTalker:GetShootPos(),
		endpos = pListener:GetShootPos(),
		filter = {pTalker, pListener},
		mask = MASK_SHOT
	})
	
	local bHasHit = tTrace.Hit

	if bHasHit and (EVoice.Constants["config"]["texturesLOSWhitelist"] or {})[tTrace.HitTexture] then
		bHasHit = false
	end

	if bHasHit and string.find(tTrace.HitTexture:lower(), "glass") then
		bHasHit = false
	end

	if bHasHit and IsValid(tTrace.Entity) then
	
		local eTarget = tTrace.Entity

		if eTarget:IsPlayer() or eTarget:IsNPC() or (EVoice.Constants["config"]["classesLOSWhitelist"] or {})[eTarget:GetClass()] then
			bHasHit = false
		end

		if string.find(eTarget:GetClass():lower(), "door") then
			bHasHit = (self:CalculateDistance(pTalker, pListener, tDistanceCache) > EVoice.Constants["config"]["doorTalkDistance"])
		end

		if eTarget:IsVehicle() then
			bHasHit = (self:CalculateDistance(pTalker, pListener, tDistanceCache) > EVoice.Constants["config"]["vehicleTalkDistance"])
		end

	end

	return bHasHit

end