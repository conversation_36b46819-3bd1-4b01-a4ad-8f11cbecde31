local language_code = "FR"

AWarn.Localization:RegisterLanguage( language_code, "Français" )

//Credit: https://www.gmodstore.com/users/76561198047157426

AWarn.Localization:AddDefinition( language_code, "welcome1", 					"Bienvenue sur AWarn3!" )
AWarn.Localization:AddDefinition( language_code, "insufficientperms", 			"Permissions insuffisantes pour exécuter cette commande." )
AWarn.Localization:AddDefinition( language_code, "insufficientperms2", 			"Permissions insuffisantes pour visualiser les avertissements de ce joueur." )
AWarn.Localization:AddDefinition( language_code, "commandnonexist", 			"Cette commande n'existe pas." )
AWarn.Localization:AddDefinition( language_code, "invalidtargetid", 			"Cible ou ID invalide." )
AWarn.Localization:AddDefinition( language_code, "invalidtarget", 				"Cible invalide." )
AWarn.Localization:AddDefinition( language_code, "reasonrequired", 				"Motif d'avertissement requis." )
AWarn.Localization:AddDefinition( language_code, "remove1activewarn", 			"Vous avez supprimé 1 avertissement actif de" )
AWarn.Localization:AddDefinition( language_code, "deletedwarningid", 			"ID d'avertissement supprimé" )
AWarn.Localization:AddDefinition( language_code, "removeallwarnings", 			"Vous avez supprimé tous les avertissements de" )
AWarn.Localization:AddDefinition( language_code, "deletedwarningsfor", 			"Suppression de tous les avertissements pour" )
AWarn.Localization:AddDefinition( language_code, "cantopenconsole", 			"Vous ne pouvez pas ouvrir le menu à partir de la console du serveur." )
AWarn.Localization:AddDefinition( language_code, "invalidoption", 				"Option Invalide." )
AWarn.Localization:AddDefinition( language_code, "invalidoptionvaluetype",		"Type de valeur d'option invalide." )
AWarn.Localization:AddDefinition( language_code, "optionsloaded",				"Options chargées!" )
AWarn.Localization:AddDefinition( language_code, "nopunishment",				"Aucune punition pour ce nombre d'avertissement." )
AWarn.Localization:AddDefinition( language_code, "punishmentsloaded",			"Punitions chargées!" )
AWarn.Localization:AddDefinition( language_code, "playernotallowedwarn",		"Ce joueur n'a pas le droit d'être averti." )
AWarn.Localization:AddDefinition( language_code, "warnmessage1",				"Vous avez reçu un avertissement de %s pour %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage2",				"Vous avez donné un avertissement à %s pour %s" )
AWarn.Localization:AddDefinition( language_code, "warnmessage3",				"%s a reçu un avertissement de %s pour %s" )
AWarn.Localization:AddDefinition( language_code, "warnmessage4",				"Vous avez reçu un avertissement de %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage5",				"Vous avez donné un avertissement à %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage6",				"%s a reçu un avertissement de %s." )
AWarn.Localization:AddDefinition( language_code, "joinmessage1",				"a rejoint le serveur avec des avertissements." )
AWarn.Localization:AddDefinition( language_code, "joinmessage2",				"Son dernier avertissement était :" )
AWarn.Localization:AddDefinition( language_code, "joinmessage3",				"Bienvenue sur le serveur ! Il semble que vous avez été avertis récemment." )
AWarn.Localization:AddDefinition( language_code, "joinmessage4",				"Vous pouvez consulter vos avertissements à tout moment en tapant" )
AWarn.Localization:AddDefinition( language_code, "closemenu",					"Fermer le menu" )
AWarn.Localization:AddDefinition( language_code, "searchplayers",				"Trouver des joueurs" )
AWarn.Localization:AddDefinition( language_code, "viewwarnings",				"Avertissements" )
AWarn.Localization:AddDefinition( language_code, "configuration",				"Configuration" )
AWarn.Localization:AddDefinition( language_code, "clientoptions",				"Options utilisateur" )
AWarn.Localization:AddDefinition( language_code, "serveroptions",				"Options serveur" )
AWarn.Localization:AddDefinition( language_code, "colorcustomization",			"Personnalisation des couleurs" )
AWarn.Localization:AddDefinition( language_code, "colorselection",				"Choix des couleurs" )
AWarn.Localization:AddDefinition( language_code, "languageconfiguration",		"Choix de la lanque" )
AWarn.Localization:AddDefinition( language_code, "selectlanguage",				"Sélectionnez une langue" )
AWarn.Localization:AddDefinition( language_code, "enablekickpunish",			"Activer le kick en punition" )
AWarn.Localization:AddDefinition( language_code, "enablebanpunish",				"Activer le ban en punition" )
AWarn.Localization:AddDefinition( language_code, "enabledecay",					"Activer l'expiration des avertissements" )
AWarn.Localization:AddDefinition( language_code, "resetafterban",				"Réinitialiser les avertissements actifs après le ban" )
AWarn.Localization:AddDefinition( language_code, "allowwarnadmins",				"Activer la possibilité de warn les admins" )
AWarn.Localization:AddDefinition( language_code, "pressenter",					"Appuyez sur Entrée pour enregistrer les changements" )
AWarn.Localization:AddDefinition( language_code, "entertosave",					"Entrer pour enregistrer" )
AWarn.Localization:AddDefinition( language_code, "chatprefix",					"Préfixe du Chat" )
AWarn.Localization:AddDefinition( language_code, "warningdecayrate",			"Temps d'expiration des avertissements (en minutes)" )
AWarn.Localization:AddDefinition( language_code, "serverlanguage",				"Langue du serveur" )
AWarn.Localization:AddDefinition( language_code, "punishmentsconfiguration",	"Configuration des punitions" )
AWarn.Localization:AddDefinition( language_code, "addpunishment",				"Ajouter une punition" )
AWarn.Localization:AddDefinition( language_code, "warnings",					"Avertissements" )
AWarn.Localization:AddDefinition( language_code, "punishtype",					"Type de punition" )
AWarn.Localization:AddDefinition( language_code, "punishlength",				"Temps de la punition" )
AWarn.Localization:AddDefinition( language_code, "playermessage",				"Message du joueur" )
AWarn.Localization:AddDefinition( language_code, "playername",					"Pseudo" )
AWarn.Localization:AddDefinition( language_code, "messagetoplayer",				"Message au joueur" )
AWarn.Localization:AddDefinition( language_code, "servermessage",				"Message du serveur" )
AWarn.Localization:AddDefinition( language_code, "messagetoserver",				"Message au serveur" )
AWarn.Localization:AddDefinition( language_code, "deletewarning",				"Supprimer l'avertissement" )
AWarn.Localization:AddDefinition( language_code, "punishaddmenu",				"Menu d'ajout de punitions" )
AWarn.Localization:AddDefinition( language_code, "inminutes",					"En Minutes" )
AWarn.Localization:AddDefinition( language_code, "0equalperma",					"0 = Permanent" )
AWarn.Localization:AddDefinition( language_code, "use%",						"Utiliser %s pour afficher le nom du joueur" )
AWarn.Localization:AddDefinition( language_code, "setdefault",					"Définir la valeur par défaut" )
AWarn.Localization:AddDefinition( language_code, "showingownwarnings",			"Affichage de vos propres avertissements" )
AWarn.Localization:AddDefinition( language_code, "warnedby",					"Averti par" )
AWarn.Localization:AddDefinition( language_code, "warningserver",				"Serveur d'avertissement" )
AWarn.Localization:AddDefinition( language_code, "warningreason",				"Raison  de l'avertissement" )
AWarn.Localization:AddDefinition( language_code, "warningdate",					"Date de l'avertissement" )
AWarn.Localization:AddDefinition( language_code, "nothing",						"RIEN" )
AWarn.Localization:AddDefinition( language_code, "submit",						"Envoyer" )
AWarn.Localization:AddDefinition( language_code, "connectedplayers",			"Joueurs en ligne" )
AWarn.Localization:AddDefinition( language_code, "displaywarningsfor",			"Affichage des avertissements pour" )
AWarn.Localization:AddDefinition( language_code, "activewarnings",				"Avertissements actifs" )
AWarn.Localization:AddDefinition( language_code, "selectedplayernowarnings",	"Le joueur sélectionné n'a pas d'avertissement enregistrer." )
AWarn.Localization:AddDefinition( language_code, "selectplayerseewarnings",		"Sélectionnez un joueur pour voir ses avertissements." )
AWarn.Localization:AddDefinition( language_code, "warnplayer",					"Avertir le joueur" )
AWarn.Localization:AddDefinition( language_code, "reduceactiveby1",				"Réduire les avertissements actifs de 1" )
AWarn.Localization:AddDefinition( language_code, "playerwarningmenu",			"Menu d'avertissement du joueur" )
AWarn.Localization:AddDefinition( language_code, "playersearchmenu",			"Menu de recherche de joueur" )
AWarn.Localization:AddDefinition( language_code, "warningplayer",				"Avertissement Joueur" )
AWarn.Localization:AddDefinition( language_code, "excludeplayers",				"Exclure les joueurs qui n'ont pas d'historique d'avertissement" )
AWarn.Localization:AddDefinition( language_code, "searchforplayers",			"Rechercher des joueurs par leur nom ou leur SteamID64" )
AWarn.Localization:AddDefinition( language_code, "name",						"Nom" )
AWarn.Localization:AddDefinition( language_code, "lastplayed",					"Dernière connexion" )
AWarn.Localization:AddDefinition( language_code, "lastwarned",					"Dernier avertissement" )
AWarn.Localization:AddDefinition( language_code, "never",						"Jamais" )
AWarn.Localization:AddDefinition( language_code, "playerid",					"ID du Joueur" )
AWarn.Localization:AddDefinition( language_code, "lookupplayerwarnings",		"Consulter les avertissements de ce joueur" )
AWarn.Localization:AddDefinition( language_code, "servername",					"Nom du serveur" )
AWarn.Localization:AddDefinition( language_code, "clientjoinmessage",			"Afficher le nombre d'avertissements au joueur lors de l'adhésion" )
AWarn.Localization:AddDefinition( language_code, "adminjoinmessage",			"Afficher un message aux administrateurs lorsque le joueur se joint avec des avertissements" )
AWarn.Localization:AddDefinition( language_code, "punishmentoptions",			"Les sanctions" )
AWarn.Localization:AddDefinition( language_code, "kickpunishdescription",		"S'il est activé, AWarn3 peut expulser les joueurs du serveur en guise de punition." )
AWarn.Localization:AddDefinition( language_code, "banpunishdescription",		"S'il est activé, AWarn3 peut bannir les joueurs du serveur en guise de punition." )
AWarn.Localization:AddDefinition( language_code, "enabledecaydescription",		"Si cette option est activée, les avertissements actifs s'atténueront avec le temps." )
AWarn.Localization:AddDefinition( language_code, "reasonrequireddescription",	"Si cette option est activée, les administrateurs devront fournir une raison dans leur avertissement." )
AWarn.Localization:AddDefinition( language_code, "resetafterbandescription",	"Si activé, les avertissements actifs d'un utilisateur seront réinitialisés à 0 après avoir été bannis par AWarn3." )
AWarn.Localization:AddDefinition( language_code, "logevents",					"Consigner les événements d'avertissement." )
AWarn.Localization:AddDefinition( language_code, "logeventsdescription",		"Si activé, les actions dans AWarn3 seront enregistrées dans un fichier texte." )
AWarn.Localization:AddDefinition( language_code, "allowwarnadminsdescription",	"Si activé, les administrateurs pourront avertir les autres administrateurs." )
AWarn.Localization:AddDefinition( language_code, "clientjoinmessagedescription","Si activé, les utilisateurs qui rejoignent le serveur verront un message dans le chat s'ils ont des avertissements." )
AWarn.Localization:AddDefinition( language_code, "adminjoinmessagedescription",	"Si cette option est activée, les administrateurs sur le serveur verront quand un joueur se joint à lui qui a des avertissements." )
AWarn.Localization:AddDefinition( language_code, "chatprefixdescription",		"La commande chat utilisée pour les commandes AWarn3. Par défaut : !warn" )
AWarn.Localization:AddDefinition( language_code, "warningdecayratedescription",	"Le temps (en minutes) qu'un joueur doit être connecté pour qu'un avertissement actif se désintègre." )
AWarn.Localization:AddDefinition( language_code, "servernamedescription",		"Le nom de ce serveur. Ceci est utile pour les configurations de plusieurs serveurs." )
AWarn.Localization:AddDefinition( language_code, "selectlanguagedescription",	"Il s'agit de la langue dans laquelle les messages du serveur seront affichés." )
AWarn.Localization:AddDefinition( language_code, "theme",						"Thème de l'interface" )
AWarn.Localization:AddDefinition( language_code, "themeselect",					"Sélectionne un thème" )
AWarn.Localization:AddDefinition( language_code, "punishgroup",					"Groupe de punition" )
AWarn.Localization:AddDefinition( language_code, "grouptoset",					"Groupe à définir" )
AWarn.Localization:AddDefinition( language_code, "viewnotes",					"Afficher les notes des joueurs" )
AWarn.Localization:AddDefinition( language_code, "playernotes",					"Notes du joueur" )
AWarn.Localization:AddDefinition( language_code, "interfacecustomizations",		"Personnalisations de l'interface" )
AWarn.Localization:AddDefinition( language_code, "enableblur",					"Activer le flou d'arrière-plan" )
AWarn.Localization:AddDefinition( language_code, "chooseapreset",				"Choisissez un préréglage (facultatif)" )
AWarn.Localization:AddDefinition( language_code, "warningpresets",				"Préconfigurations" )
AWarn.Localization:AddDefinition( language_code, "addeditpreset",				"Ajouter/Modifier un préréglage" )
AWarn.Localization:AddDefinition( language_code, "presetname",					"Nom du préréglage" )
AWarn.Localization:AddDefinition( language_code, "presetreason",				"Motif prédéfini" )
