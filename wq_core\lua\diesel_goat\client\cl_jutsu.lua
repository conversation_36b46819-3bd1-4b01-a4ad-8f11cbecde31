---------------------------------------
---@author: Diesel
---@time: 28/02/2025 00:46
---@version: 1.0.0
---@server: ©️ ATG - Naruto RP
---------------------------------------

DATG.Binds = {};

DATG.Binds.Jutsu1 = CreateClientConVar("jutsu_1", "", true, true, "")
DATG.Binds.Jutsu2 = CreateClientConVar("jutsu_2", "", true, true, "")
DATG.Binds.Jutsu3 = CreateClientConVar("jutsu_3", "", true, true, "")
DATG.Binds.Jutsu4 = CreateClientConVar("jutsu_4", "", true, true, "")
DATG.Binds.Jutsu5 = CreateClientConVar("jutsu_5", "", true, true, "")
DATG.Binds.Jutsu6 = CreateClientConVar("jutsu_6", "", true, true, "")

DATG.Binds.Jutsu1_Key = CreateClientConVar("kjutsu_1", "", true, true, "")
DATG.Binds.Jutsu2_Key = CreateClientConVar("kjutsu_2", "", true, true, "")
DATG.Binds.Jutsu3_Key = CreateClientConVar("kjutsu_3", "", true, true, "")
DATG.Binds.Jutsu4_Key = CreateClientConVar("kjutsu_4", "", true, true, "")
DATG.Binds.Jutsu5_Key = CreateClientConVar("kjutsu_5", "", true, true, "")
DATG.Binds.Jutsu6_Key = CreateClientConVar("kjutsu_6", "", true, true, "")

DATG.Binds.Kenjutsu1_Key = CreateClientConVar("kkenjutsu_1", "", true, true, "")
DATG.Binds.Kenjutsu2_Key = CreateClientConVar("kkenjutsu_2", "", true, true, "")
DATG.Binds.Kenjutsu3_Key = CreateClientConVar("kkenjutsu_3", "", true, true, "")

DATG.Binds.FightStyle = CreateClientConVar("fightstyle", "2", true, true, "")

DATG.Binds.Radio = CreateClientConVar("radio_speak", "", true, true, "")

DATG.Binds.Tree = CreateClientConVar("jutsu_tree", "", true, true, "")
DATG.Binds.Developer = CreateClientConVar("dev", "0", true, true, "")
DATG.Binds.Permutation = CreateClientConVar("kpermutation", "", true, true, "")
DATG.Binds.Deck = CreateClientConVar("kChangeDeck", "24", true, true, "")

DATG.Binds.OSTMenu = CreateClientConVar("ostmenu", "25", true, true, "")
DATG.Binds.AnimationMenu = CreateClientConVar("animmenu", "0", true, true, "")
-- DATG.Binds.MenuJutsu = CreateClientConVar("jutsumenu", "95", true, true, "")
DATG.Binds.Ticket = CreateClientConVar("faireticket", "93", true, true, "")
DATG.Binds.Admin = CreateClientConVar("adminmenu", "0", true, true, "")
DATG.Binds.VoiceMode = CreateClientConVar("voicetouche", "18", true, true, "")
DATG.Binds.CinematicMode = CreateClientConVar("cinematicmode", "9", true, true, "")
DATG.Binds.FightMode = CreateClientConVar("fightmode", "109", true, true, "")


local see_bone = CreateClientConVar("see_bone", "", true, true, "")
local HUD_JUTSU = {};

local mat = {
    Material("atg_hud/nuage_back.png"),
    Material("atg_hud/nuage_0.png"),

    Material("atg_hud/health_back.png"),
    Material("atg_hud/health_bar.png"),

    Material("atg_hud/chakra_back.png"),
    Material("atg_hud/chakra_bar.png"),

    Material("atg_hud/hunger_back.png"),
    Material("atg_hud/hunger_bar.png"),

    Material("atg_hud/coeur_icon.png"),
    Material("atg_hud/chakra_icon.png"),
    Material("atg_hud/ramen_icon.png"),

    -----

    Material("atg_hud/nuage_1.png"),
    Material("atg_hud/ryo_back.png"),
    Material("atg_hud/ryo.png"),
    Material("atg_hud/crosshair.png"),
    Material("atg_hud/logo.png"),
    Material("atg_hud/nuage_2.png"),

    ----

    Material("atg_hud/jutsu_back.png"),
    Material("atg_hud/progress_jutsu.png"),
    Material("atg_hud/jutsu_key.png"),

}

local function DrawCircle(xPos, yPos, radius, max, mat, color)
    local segments = {}

    segments[#segments + 1] = { x = xPos, y = yPos }

    for i = 1, max do
        local x, y = math.cos(-math.rad(90 + i)), math.sin(-math.rad(90 + i))

        segments[#segments + 1] = { x = xPos + (x * radius), y = yPos - (y * radius) }
    end


    surface.SetDrawColor(color or color_white)
    if mat == nil then
        draw.NoTexture()
    else
        surface.SetMaterial(mat)
    end
    surface.DrawPoly(segments)
end

local fileBase2 = {
    [1] = { key = 0, jutsu = 0 },
    [2] = { key = 0, jutsu = 0 },
    [3] = { key = 0, jutsu = 0 },
    [4] = { key = 0, jutsu = 0 },
    [5] = { key = 0, jutsu = 0 },
    [6] = { key = 0, jutsu = 0 },
    [7] = { key = 0 },
    [8] = { key = 0 },
    [9] = { key = 0 }
}
local fileBase3 = { [1] = fileBase2, [2] = fileBase2, [3] = fileBase2 }
local fileBase = { [1] = fileBase3, [2] = fileBase3, [3] = fileBase3 }

function DATG:RetrieveBinds(charId, tree)
    local base = {}

    if tree == 3 and not LocalPlayer():IsPremium() then
        surface.PlaySound("garrysmod/balloon_pop_cute.wav")
        DATG:RetrieveBinds(charId, 1)
        notification.AddLegacy("Vous n'êtes pas vip vous ne pouvez utiliser l'arbre n°3 !", 5, 1)
        return
    end

    if not file.Exists("atg_binds.json", "DATA") then
        file.Write("atg_binds.json", util.TableToJSON(fileBase))
    end

    base = util.JSONToTable(file.Read("atg_binds.json", "DATA"))

    for i = 1, 6 do
        DATG.Binds["Jutsu" .. i]:SetInt(tonumber(base[charId][tree][i].jutsu))
        DATG.Binds["Jutsu" .. i .. "_Key"]:SetInt(tonumber(base[charId][tree][i].key))
    end

    for i = 1, 3 do
        DATG.Binds["Kenjutsu" .. i .. "_Key"]:SetInt(tonumber(base[charId][tree][i + 6].key))
    end

    -- ATG_V2.Chat:Notify("BINDS", color_white, " !")
    --
    HUD_JUTSU[#HUD_JUTSU + 1] = { name = "Arbre seléctionné : " .. tree, cur = CurTime() + 2 }
    surface.PlaySound("atg_v2/other/change_tree.mp3")

    DATG.Binds.Tree:SetInt(tree)
end

function DATG:SaveBinds()
    if not file.Exists("atg_binds.json", "DATA") then
        return
    end

    local charId = LocalPlayer():GetNWInt("CharacterID", 1)

    local result = fileBase2
    local base = util.JSONToTable(file.Read("atg_binds.json", "DATA"))

    for i = 1, 6 do
        result[i].jutsu = DATG.Binds["Jutsu" .. i]:GetInt()
        result[i].key = DATG.Binds["Jutsu" .. i .. "_Key"]:GetInt()
    end

    for i = 1, 3 do
        result[i + 6].key = DATG.Binds["Kenjutsu" .. i .. "_Key"]:GetInt()
    end

    base[charId][DATG.Binds.Tree:GetInt()] = result;

    file.Write("atg_binds.json", util.TableToJSON(base))

    -- print("[ConVar] Binds saved !")

  --  ATG_V2.Chat:Notify("BINDS", Color(0, 255, 0), "Vos binds ont été sauvegardé !")
end

net.Receive("ATG:OwnedJutsu", function()
    LocalPlayer().ATG_Jutsu = {}

    print("[Diesel] Start to syncronise ur jutsu !")

    local jutsu_count = net.ReadUInt(8)
    for i = 1, jutsu_count do
        LocalPlayer().ATG_Jutsu[net.ReadUInt(8)] = net.ReadUInt(3)
    end

    print("[Diesel] Finished to syncronise " .. jutsu_count .. " jutsu on ur character !")

    DATG:RetrieveBinds(LocalPlayer():GetNWInt("CharacterID", 1), 1)
end)

net.Receive("ATG:OwnedKenjutsu", function()
    LocalPlayer().ATG_Kenjutsu = {}

    print("[Diesel] Start to syncronise ur technic !")

    local kenjutsu_tech_count = net.ReadUInt(8)
    for i = 1, kenjutsu_tech_count do
        LocalPlayer().ATG_Kenjutsu[net.ReadUInt(8)] = net.ReadUInt(3)
    end

    print("[Diesel] Finished to syncronise " .. kenjutsu_tech_count .. " kenjutsu technic on ur character !")
end)

net.Receive("ATG:MyAffinity", function()
    LocalPlayer().ATG_Nature = {}

    print("[Diesel] Start to syncronise ur nature&kekkei !")

    local base_nature = net.ReadUInt(3)   --> PREMIER REROLL
    local second_nature = net.ReadUInt(3) --> NATURE REROLL
    local third_nature = net.ReadUInt(3)  --> AU KAGE
    local keikkei = net.ReadUInt(4)       -- >KEIKKEI GENKAI
    local clan = net.ReadUInt(4)          -- > Clan
    local speciality = net.ReadUInt(2)    -- > Spécialité
    local classe = net.ReadUInt(3)     -- > Classe

    LocalPlayer().ATG_Nature.Base = DATG:GetNatureById(base_nature)
    LocalPlayer().ATG_Nature.Reroll = DATG:GetNatureById(second_nature)
    ---------------------------------------------
    LocalPlayer().ATG_Nature.Gived = DATG:GetNatureById(third_nature)

    LocalPlayer().ATG_Nature.Keikkei_Genkai = DATG:GetKeikkeiGenkaiById(keikkei)

    LocalPlayer().ATG_Nature.Clans = DATG.Clans[clan];
    LocalPlayer().ATG_Nature.Speciality = speciality;
    LocalPlayer().ATG_Nature.Classe = classe;

    PrintTable(LocalPlayer().ATG_Nature)

    print("[Diesel] Finished to syncronise nature&keikkei on ur character !")
end)

local PLAYER = FindMetaTable("Player")
function PLAYER:HasJutsu(id)
    if self:IsDev() then return true end;
    local jutsu = DATG.Technics[id]
    if self:IsAnimateur() and (jutsu.Rang == "C" or jutsu.Rang == "B") and not table.HasValue(DATG.Autres, jutsu.Nature) then 
        return true 
    end;
    local owned_jutsu = (self.ATG_Jutsu == nil and {} or self.ATG_Jutsu)

    if owned_jutsu[id] != nil then
        return true
    end

    return false
end

function PLAYER:HasKenjutsu(id)
    if self:IsDev() then return true end;
    local owned_jutsu = (self.ATG_Kenjutsu == nil and {} or self.ATG_Kenjutsu)

    if owned_jutsu[id] != nil then
        return true
    end

    return false
end

-- RunConsoleCommand("removepanel")

local MODEL_PANEL, y, x = nil, {}, {};
local IsFightMode = false;
local localpos = { x = 0, y = 0 }
local CD, CD_KEN = {}, {}
local CHAKRA_DISPLAY = {}
local KEYS = {}
local lerpXp = 0;
local curXP = 0;
local CurDeath = CurTime()

local currentChakra, currentFood, currentHealth = 0, 0, 0;
local CurVoice = CurTime()



timer.Simple(1, function()
    for k, v in pairs(vgui.GetAll()) do
        if v:GetName() == "ATG.ModelPanel" and IsValid(v) then
            v:Remove()
        end
    end
end)

local function DrawMusicDisplay(music)

    
    if not (music and music.Sound and music.Sound:IsValid()) then 
        return 
    end
    

    local musicName = ATG_V2.OST:GetSoundByPath(music.path).sName or "Inconnu"
    

    local currentTime = music.Sound:GetTime()
    local totalTime = music.Sound:GetLength()
    local progressRatio = currentTime / totalTime
    
    draw.SimpleText(musicName, ATG_V2.Fonts("ATG", 7, 500), SW(1625), SH(100), color_white, TEXT_ALIGN_LEFT)
    
    draw.SimpleText(string.format("%02d:%02d", math.floor(currentTime / 60), currentTime % 60), ATG_V2.Fonts("ATG", 7, 500), SW(1625), SH(150), color_white, TEXT_ALIGN_LEFT)

    draw.SimpleText(string.format("%02d:%02d", math.floor(totalTime / 60), totalTime % 60), ATG_V2.Fonts("ATG", 7, 500), SW(1880), SH(150), color_white, TEXT_ALIGN_RIGHT)

    draw.RoundedBox(6, SW(1625), SH(140), SW(258), SH(3), Color(217, 217, 217, 255))
    
    local pos = math.Clamp(progressRatio * 255, 0, 245)
    
    draw.RoundedBox(50, SW(1625) + SW(pos), SH(135), SW(13), SH(14), Color(100, 100, 100, 255))

end

local chakra_data = {}
local waithttp = {}
local function getChakraAmount(id, niv)
    
    if chakra_data[id] != nil then
        if chakra_data[id][niv] != nil then
            return chakra_data[id][niv];
        end
    end
    
    if id == nil or niv == nil then return "?"; end
    
    if waithttp[id] == nil or waithttp[id][niv] == nil then
        if waithttp[id] == nil then
            waithttp[id] = {}
        end
        
        waithttp[id][niv] = true;
        
        local apiUrl = "https://superpanel.alwaysdata.net/getchakra.php?id=%s&chakra_id=%s"
        apiUrl = apiUrl:format(id, niv)
        
        http.Fetch(apiUrl, function(body)
            -- print("received:"..id, body)
            
            if chakra_data[id] == nil then
                chakra_data[id] = {}
            end
            
            chakra_data[id][niv] = tonumber(body)
        end, function()
        end)
    end
    
    return "?";
    
end


local rotateJutsu = 0;

hook.Add("HUDPaint", "Diesel:ATG:Jutsu", function()
    if ATG_V2 == nil then return end

    local ply = LocalPlayer()

    if not IsValid(ply) then return end
    if (ply:AdminMode() and not ply:IsAnimateur()) or not ply:Alive() then
        if IsValid(MODEL_PANEL) then MODEL_PANEL:Remove() end
        return
    end

    if ply.Cinematique then
        if IsValid(MODEL_PANEL) then
            MODEL_PANEL:Remove()
        end
        return
    end

    local isKenjutsuMod = (ply.Kenjutsu != nil and ply.Kenjutsu.sClass != nil and ply:GetNetworkVar("KenjutsuOut", false))

    -- if not isKenjutsuMod then
    for k, v in pairs(HUD_JUTSU) do
        if v.cur < CurTime() then
            HUD_JUTSU[k] = nil;
            HUD_JUTSU = table.ClearKeys(HUD_JUTSU)
        end


        draw.SimpleText(v.name, ATG_V2.Fonts("ATG", 8, 500), ScrW() / 2, ((ScrH() * 0.87) - ((ScrH() * 0.03) * (k - 1))),
            Color(255, 255, 255, ((v.cur - CurTime()) / 0.3) * 255), TEXT_ALIGN_CENTER)
    end
    -- end

    KEYS["Ouvrir le menu OST"] = function(ply)
        return input.GetKeyCode("O")
    end
 
    currentHealth = Lerp(FrameTime() * 5.0, currentHealth or 0, ply:Health())
    currentChakra = Lerp(FrameTime() * 5.0, math.Clamp(currentChakra, 0, ply:GetNWInt("CTG_Chakra:MaxChakra", 0)) or 0,
        ply:GetNWInt("CTG_Chakra:CurrentChakra", 0))
    currentFood = Lerp(FrameTime() * 5.0, currentFood or 0, ply:getDarkRPVar("Energy"))


    surface.SetMaterial(ATG_V2:Material("hud/health_bar.png"))
    surface.SetDrawColor(color_white)
    surface.DrawTexturedRect(SW(170), SH(917), SW(320), SH(28))
    masks.Start()
    surface.SetMaterial(ATG_V2:Material("hud/health_mask.png"))
    surface.SetDrawColor(color_white)
    surface.DrawTexturedRect(SW(170), SH(919), SW(318), SH(24))
    masks.Source()
    draw.RoundedBox(0, SW(170), SH(919),
        SW(318) * (math.Clamp(currentHealth, 0, ply:GetMaxHealth()) / ply:GetMaxHealth()), SH(24), color_white)
    masks.End()

    surface.SetFont(ATG_V2.Fonts("ATG", 7, 500))
    local sw, __ = surface.GetTextSize(math.floor(currentHealth) .. "/" .. ply:GetMaxHealth())

    draw.SimpleText(math.floor(currentHealth) .. "/" .. ply:GetMaxHealth(), ATG_V2.Fonts("ATG", 7, 500), SW(460), SH(920),
        Color(255, 255, 255, 170), TEXT_ALIGN_RIGHT)

    surface.SetMaterial(ATG_V2:Material("hud/heart.png"))
    surface.SetDrawColor(255, 255, 255, 170)
    surface.DrawTexturedRect(SW(460) - sw - SW(19), SH(923.5), SW(15), SH(15))

    surface.SetMaterial(ATG_V2:Material("hud/chakra_bar.png"))
    surface.SetDrawColor(color_white)
    surface.DrawTexturedRect(SW(177), SH(947), SW(293), SH(28))
    masks.Start()
    surface.SetMaterial(ATG_V2:Material("hud/chakra_mask.png"))
    surface.SetDrawColor(color_white)
    surface.DrawTexturedRect(SW(177), SH(949), SW(291), SH(24))
    masks.Source()
    draw.RoundedBox(0, SW(177), SH(949),
        SW(291) *
        (math.Clamp(currentChakra, 0, ply:GetNWInt("CTG_Chakra:MaxChakra", 0)) / ply:GetNWInt("CTG_Chakra:MaxChakra", 0)),
        SH(24), color_white)
    masks.End()

    surface.SetFont(ATG_V2.Fonts("ATG", 7, 500))
    local sw, __ = surface.GetTextSize(math.floor(currentChakra) .. "/" .. ply:GetNWInt("CTG_Chakra:MaxChakra", 0))

    draw.SimpleText(math.floor(currentChakra) .. "/" .. ply:GetNWInt("CTG_Chakra:MaxChakra", 0),
        ATG_V2.Fonts("ATG", 7, 500), SW(440), SH(950), Color(255, 255, 255, 170), TEXT_ALIGN_RIGHT)

    surface.SetMaterial(ATG_V2:Material("hud/chakra.png"))
    surface.SetDrawColor(255, 255, 255, 170)
    surface.DrawTexturedRect(SW(440) - sw - SW(19), SH(954.5), SW(10), SH(9))

    surface.SetMaterial(ATG_V2:Material("hud/hunger_bar.png"))
    surface.SetDrawColor(color_white)
    surface.DrawTexturedRect(SW(171), SH(977), SW(162), SH(28))
    masks.Start()
    surface.SetMaterial(ATG_V2:Material("hud/hunger_mask.png"))
    surface.SetDrawColor(color_white)
    surface.DrawTexturedRect(SW(171), SH(979), SW(160), SH(24))
    masks.Source()
    draw.RoundedBox(0, SW(171), SH(979), SW(160) * (currentFood / 100), SH(24), color_white)
    masks.End()

    surface.SetFont(ATG_V2.Fonts("ATG", 7, 500))
    local sw, __ = surface.GetTextSize(math.floor(currentFood) .. "/100")

    draw.SimpleText(math.floor(currentFood) .. "/100", ATG_V2.Fonts("ATG", 7, 500), SW(303), SH(980),
        Color(255, 255, 255, 170), TEXT_ALIGN_RIGHT)

    surface.SetMaterial(ATG_V2:Material("hud/ramen.png"))
    surface.SetDrawColor(255, 255, 255, 170)
    surface.DrawTexturedRect(SW(303) - sw - SW(19), SH(983), SW(13), SH(13))

    surface.SetMaterial(ATG_V2:Material("hud/name_bar.png"))
    surface.SetDrawColor(color_white)
    surface.DrawTexturedRect(SW(139), SH(1011), SW(198), SH(25))

    draw.SimpleTextOutlined(ply:Nick(), ATG_V2.Fonts("ATG", 7, 500), SW(191), SH(1013), Color(206, 206, 206),
        TEXT_ALIGN_LEFT, 0, 1, Color(55, 55, 55))

    surface.SetMaterial(ATG_V2:Material("hud/nuage_2.png"))
    surface.SetDrawColor(color_white)
    surface.DrawTexturedRect(SW(126), SH(864), SW(128), SH(87))

    surface.SetMaterial(ATG_V2:Material("hud/circle.png"))
    surface.SetDrawColor(color_white)
    surface.DrawTexturedRect(SW(4.62), SH(856), SW(228), SH(224))

    if ply:IsSpeaking() then
        surface.SetMaterial(ATG_V2:Material("hud/mic.png"))
        surface.SetDrawColor(color_white)
        surface.DrawTexturedRect(SW(491), SH(948), SW(34), SH(34))
    end

    surface.SetMaterial(ATG_V2:Material("hud/ryo.png", "smooth noclamp"))
    surface.SetDrawColor(color_white)
    surface.DrawTexturedRect(0, 0, SW(235), SH(133))

    local money = string.Comma(ply:getDarkRPVar("money"), ".")

    draw.SimpleText(money, ATG_V2.Fonts("ATG", 7, 500), SW(102), SH(52), color_white, TEXT_ALIGN_LEFT)

    surface.SetMaterial(ATG_V2:Material("hud/logo_new.png"))
    surface.SetDrawColor(color_white)
    surface.DrawTexturedRect(SW(1725), SH(-30), SW(200), SH(180))

    local music = ATG_V2.OST.HudMusic
    
    DrawMusicDisplay(music)

    lerpXp = Lerp(FrameTime() * 1.5, lerpXp, ply:GetXP())
    
    if math.Round(lerpXp) < ply:GetXP() then
        curXP = CurTime() + 2
    end
    
    if curXP > CurTime() then
        local alpha = 255 * ((curXP - CurTime()) / 2)
        surface.SetMaterial(ATG_V2:Material("hud/xp.png"))
        surface.SetDrawColor(255, 255, 255, alpha)
        surface.DrawTexturedRect(SW(622), SH(2), SW(676), SH(125))
    
        masks.Start()
        surface.SetMaterial(ATG_V2:Material("hud/xp_mask.png"))
        surface.SetDrawColor(255, 255, 255, alpha)
        surface.DrawTexturedRect(SW(625), SH(29), SW(669), SH(30))
        masks.Source()
        draw.RoundedBox(0, SW(625), SH(29),
            SW(669) *
            math.Remap(ply:GetXP(), ATG_V2.Lvl:GetXPFromLevel(ply:GetLvl()), ATG_V2.Lvl:GetXPToNextLevel(ply:GetXP()), 0, 1),
            SH(30), Color(255, 255, 255, alpha))
        masks.End()
        
        draw.SimpleText(string.Comma(ply:GetXP(), " ") .. " / " ..
            string.Comma(ATG_V2.Lvl:GetXPToNextLevel(ply:GetXP()), " "), ATG_V2.Fonts("ATG", 6, 500), SW(1920 / 2), SH(34),
            Color(255, 255, 255, alpha * 0.47), TEXT_ALIGN_CENTER)
        draw.SimpleTextOutlined("NIV.", ATG_V2.Fonts("ATG", 8, 500), SW(943), SH(65), Color(206, 206, 206, alpha), TEXT_ALIGN_CENTER,
            0, 1, Color(55, 55, 55, alpha))
        draw.SimpleTextOutlined(string.Comma(ply:GetLvl(), " "), ATG_V2.Fonts("ATG", 8, 500), SW(970), SH(65),
            Color(220, 169, 31, alpha), TEXT_ALIGN_LEFT, 0, 1, Color(55, 55, 55, alpha))
            
        draw.SimpleText("Votre expérience est entrain d'augmenter (+".. math.floor(ply:GetXP() - lerpXp) .."XP)", ATG_V2.Fonts("ATG2", 7, 500), SW(960), SH(120), Color(255, 255, 255, alpha), 1)
    end



    local index = 1;
    for text, getkey in SortedPairs(KEYS, false) do
        local y = SH(1060 - (index * 40))
        surface.SetFont(ATG_V2.Fonts("ATG", 8, 500))
        local w, h = surface.GetTextSize(text)
        local x = SW(1870) - w
        local key = tonumber(getkey(ply))

        draw.SimpleText(text, ATG_V2.Fonts("ATG", 8, 500), x + SW(34), y + SH(13),
            Color(240, 240, 240, 255 + (math.sin(CurTime() * 2) * 155)), TEXT_ALIGN_LEFT)

        surface.SetMaterial(ATG_V2:Key(key))
        surface.SetDrawColor((input.IsKeyDown(key) and Color(200, 200, 200) or color_white))
        surface.DrawTexturedRect(x - SW(18), y + SH(4), SW(48), SH(48))

        index = index + 1;
    end

    if ply.CanReset then
        KEYS["Arrêter l'animation"] = function()
            return input.GetKeyCode("X")
        end
    else
        KEYS["Arrêter l'animation"] = nil;
    end

    local basex = SW(260)
    local basey = SH(885)
    local marginx = SW(30)
    for i, v in ipairs(LocalPlayer():GetAlteration()) do
        surface.SetMaterial(ATG_V2:Material(v, "smooth noclamp"))
        surface.SetDrawColor(color_white)
        surface.DrawTexturedRect(basex + ((i - 1) * marginx), basey, SW(30), SH(30))
    end

    -- if not IsValid(MINIMAP_PANEL) then
    --     local x, y = SW(20), SH(20)
    --     local border = 2

    --     -- MINIMAP_PANEL = vgui.Create("DPanel")
    --     -- MINIMAP_PANEL:SetPos(x, y)
    --     -- MINIMAP_PANEL:SetSize(SW(250), SH(250))
    --     -- MINIMAP_PANEL.Paint = function(self, w, h)
    --     --     draw.RoundedBox(0, 0, 0, w, h, color_white)
    --     --     local old = DisableClipping( true ) -- Avoid issues introduced by the natural clipping of Panel rendering
    --     --     render.RenderView( {
    --     --         origin = ply:GetPos() + Vector(0, 0, 1000),
    --     --         angles = Angle(90, 0, 0),
    --     --         x = x + (SW(border)/2), y = y + (SH(border)/2),
    --     --         w = w - SW(border), h = h - SH(border),
    --     --         fov = 90
    --     --     } )
    --     --     DisableClipping( old )
    --     -- end
    -- end

    if not IsValid(MODEL_PANEL) and not stop then
        local char = ply:ATG_GetCharacter()
        if not char then return end

        local x, y = SW(10), SH(870)
        MODEL_PANEL = vgui.Create("DPanel")
        MODEL_PANEL:SetPos(SW(0), SH(400))  -- Déplacé vers le haut
        MODEL_PANEL:SetSize(SW(300), SH(380))
        MODEL_PANEL.Paint = nil;
        MODEL_PANEL:SetCursor("arrow")
        MODEL_PANEL.PaintOver = function()
            surface.SetMaterial(ATG_V2:Material("hud/decor_circle.png"))
            surface.SetDrawColor(color_white)
            surface.DrawTexturedRect(SW(28), SH(183), SW(177), SH(173))
        end

        local base = vgui.Create("ATG.ModelPanel", MODEL_PANEL)
        base:SetPos(SW(0), SH(80))
        base:SetSize(SW(300), SH(300))
        base:SetModel(ply:GetModel())
        base:SetCursor("arrow")
        if (not ply:AdminMode() or ply:IsAnimateur()) then
            base:SetPlayerChildren(LocalPlayer())
        end
        base:SetFOV(90)
        local ent = base:GetEntity()
        ent:SetSequence(ent:LookupSequence("nrp_lobby_choji_spl_type01_cut_loop"))
        base.LayoutEntity = function()
            -- if ent:GetCycle() == 1 then ent:SetCycle(0) end
            ent:SetCycle(0.5)
            -- base:RunAnimation()
        end

        local function circle(x, y, radius, seg)
            local cir = {}

            table.insert(cir, { x = x, y = y, u = 0.5, v = 0.5 })
            for i = 0, seg do
                local a = math.rad((i / seg) * -360)
                table.insert(cir,
                    {
                        x = x + math.sin(a) * radius,
                        y = y + math.cos(a) * radius,
                        u = math.sin(a) / 2 + 0.5,
                        v = math
                            .cos(a) / 2 + 0.5
                    })
            end

            local a = math.rad(0)
            table.insert(cir,
                {
                    x = x + math.sin(a) * radius,
                    y = y + math.cos(a) * radius,
                    u = math.sin(a) / 2 + 0.5,
                    v = math.cos(a) /
                        2 + 0.5
                })

            draw.RoundedBox(180, 0, y - SH(300), SW(170), SH(300), color_white)
            surface.DrawPoly(cir)
        end

        local oldFunc = base.Paint
        base.Paint = function(self, w, h)
            if not ply:Alive() then return end

            render.SetStencilWriteMask(0xFF)
            render.SetStencilTestMask(0xFF)
            render.SetStencilReferenceValue(0)
            render.SetStencilCompareFunction(STENCIL_ALWAYS)
            render.SetStencilPassOperation(STENCIL_KEEP)
            render.SetStencilFailOperation(STENCIL_KEEP)
            render.SetStencilZFailOperation(STENCIL_KEEP)
            render.ClearStencil()

            render.SetStencilEnable(true)
            render.SetStencilReferenceValue(1)

            render.SetStencilCompareFunction(STENCIL_NEVER)
            render.SetStencilFailOperation(STENCIL_REPLACE)

            cam.Start2D()
            surface.SetDrawColor(255, 255, 255, 200)
            circle(SW(119), SH(670), SW(66), 80)  -- Déplacé vers le haut
            cam.End2D()

            render.SetStencilCompareFunction(STENCIL_EQUAL)
            render.SetStencilFailOperation(STENCIL_KEEP)

            oldFunc(self, w, h)

            render.SetStencilEnable(false)
        end


        if not base.Entity then return end
        local bone = base.Entity:LookupBone("ValveBiped.Bip01_Head1")
        if not bone then return end
        local pos = base.Entity:GetBonePosition(bone) + Vector(-3, 7, 5)
        if bone ~= nil and pos ~= nil then
            base:SetLookAt(pos)
            base:SetCamPos(pos - Vector(-30, 3, 15))
            base.Entity:SetEyeTarget(pos - Vector(-15, 0, 0))
        else
            print("[ATG] HUD: PM invalide pour apperçu")
            MODEL_PANEL:Remove()
        end
    end


    if ply:GetActiveWeapon() == nil then return end

    if IsValid(ply:GetActiveWeapon()) then
        if ply:GetActiveWeapon():GetClass() != nil then
            if ply:GetActiveWeapon():GetClass() == "atg_fightmode" then
                IsFightMode = true;
            else
                IsFightMode = false;
            end
        else
            IsFightMode = false;
        end
    end
    if ( ply:GetSquadName( ) ~= "" ) then 
        KEYS["Parler dans la squade"] = function(ply)
            return DATG.Binds["Radio"]:GetInt()
        end
    else
        KEYS["Parler dans la squade"] = nil;
    end

    if ( ply:GetSquadName( ) ~= "" ) and not ply:GetNWBool( "Squad.VoiceMute" ) then
        KEYS["Muter la squad"] = function(ply)
            return input.GetKeyCode("M")
        end
        KEYS["Démuter la squad"] = nil;
    elseif ( ply:GetSquadName( ) ~= "" ) and ply:GetNWBool( "Squad.VoiceMute" ) then
        KEYS["Démuter la squad"] = function(ply)
            return input.GetKeyCode("M")
        end
        KEYS["Muter la squad"] = nil;
    else
        KEYS["Muter la squad"] = nil;
        KEYS["Démuter la squad"] = nil;
    end

    KEYS["Mode Cinématique"] = function(ply)
        return DATG.Binds.CinematicMode:GetInt()
    end
    local weapon = ply:GetActiveWeapon()
    if weapon:IsValid() and weapon:GetClass() == "atg_freecam" then
        KEYS["Reset Cam"] = function(ply)
            return input.GetKeyCode("R")
        end

        KEYS["Modifier l'angle"] = function(ply)
            return input.GetKeyCode("MOUSE1")
        end

        KEYS["Modifier la distance"] = function(ply)
            return input.GetKeyCode("MOUSE2")
        end
    else 
        KEYS["Reset Cam"] = nil;
        KEYS["Modifier l'angle"] = nil;
        KEYS["Modifier la distance"] = nil;
    end
    
    if IsFightMode or ply:GetActiveWeapon().CustomCrosshair then
        local EyeTrace = ply:GetEyeTrace()
        local pos = {}
        if EyeTrace.HitPos:Distance(EyePos()) > 0 then
            pos = EyeTrace.HitPos:ToScreen()
        else
            pos = { x = ScrW() / 2, y = ScrH() / 2 }
        end
        
        surface.SetMaterial(ATG_V2:Material("hud/crosshair.png", "smooth noclamp"))
        surface.SetDrawColor(color_white)
        surface.DrawTexturedRectRotated(pos.x, pos.y, SW(9), SH(9), 0)
    end
    
    if ply:IsCarry() then
        KEYS["Arrêter de se faire porter"] = function(ply)
            return input.GetKeyCode("E")
        end
    else
        KEYS["Arrêter de se faire porter"] = nil;
    end
    
    if not IsFightMode then
        KEYS["Changer la distance de voix"] = function(ply)
            return DATG.Binds.VoiceMode:GetInt()
        end
        KEYS["Faire un ticket"] = function(ply)
            return DATG.Binds.Ticket:GetInt()
        end
        KEYS["Changer d'arbre de jutsu"] = nil;
        KEYS["Changer de mode de combat"] = nil;
        KEYS["Permutation"] = nil;
        
        local weap = ply:GetActiveWeapon()
        
        if weap:IsValid() and weap:GetClass() == "atg_fist" then
            KEYS["Ouvrir le menu animation"] = function(ply)
                return input.GetKeyCode("P")
            end;
        end
        
    else
        KEYS["Changer la distance de voix"] = nil;
        KEYS["Faire un ticket"] = nil;
        KEYS["Changer d'arbre de jutsu"] = function(ply)
            return DATG.Binds["Deck"]:GetInt()
            --return input.GetKeyCode("N")
        end
        KEYS["Ouvrir le menu animation"] = nil;
        KEYS["Changer de mode de combat"] = function(ply)
            return DATG.Binds["FightMode"]:GetInt()
        end
        
        KEYS["Permutation"] = function()
            return DATG.Binds["Permutation"]:GetInt()
        end
        
        local dojutsu = ply:GetNetworkVar("Dojutsu", 0)
        if dojutsu != 0 then
            local technic = DATG.Technics[dojutsu]
            if technic.Dojutsu == true then
                KEYS["Activer/Désactiver votre pupille"] = function()
                    return input.GetKeyCode("0")
                end
            end
        else
            KEYS["Activer/Désactiver votre pupille"] = nil;
        end
    end

    local sSharinganJutsuSpell = LocalPlayer():GetNetworkVar("sSharinganJutsuSpell", "")
    local tSharinganSpell = DATG.Technics[sSharinganJutsuSpell]

    local alpha = (IsFightMode and 255 or 100)
    local tree = DATG.Binds["Tree"]:GetInt()
    local speciality = LocalPlayer():GetSpeciality()


    y[7] = Lerp(FrameTime() * 2, y[7] or SH(1040), (IsFightMode and SH(974) or SH(1040)))

    if not isKenjutsuMod then
        surface.SetMaterial(ATG_V2:Material("hud/tree.png"))
        surface.SetDrawColor(255, 255, 255, alpha - 100)
        surface.DrawTexturedRect(SW(1240), (y[7] or SH(1040)) + SH(27), SW(133), SH(60))

        for i = 1, 3 do
            if i == tree then
                draw.SimpleTextOutlined(i, ATG_V2.Fonts("ATG", 11, 500), SW(1240 + i * 30), y[7] + SH(31),
                    Color(255, 255, 255, alpha), TEXT_ALIGN_LEFT, 0, 1, Color(255, 255, 255, 20))
            else
                draw.SimpleText(i, ATG_V2.Fonts("ATG", 10, 500), SW(1240 + i * 30), y[7] + SH(33),
                    Color(255, 255, 255, alpha), TEXT_ALIGN_LEFT)
            end
        end
    elseif isKenjutsuMod and ply:GetSpeciality() == 1 then
        local sn = 200 + math.sin(CurTime() * 2) * 55
        draw.SimpleText("Vous ne pouvez pas utiliser les attaques de l'arme*", ATG_V2.Fonts("ATG", 5, 500), SW(960),
            SH(950), Color(163, 163, 163, sn), 1)
        draw.SimpleText("car vous n'êtes pas spécialisé en Kenjutsu", ATG_V2.Fonts("ATG", 5, 500), SW(960), SH(962),
            Color(163, 163, 163, sn), 1)
    end

    for i = 1, 6 do
        local bind = (DATG.Binds["Jutsu" .. i]:GetInt())
        local bindken;

        if i <= 3 then
            bindken = (DATG.Binds["Kenjutsu" .. i .. "_Key"]:GetInt())
        end
        
        local isActiveJutsu = ((ply:GetNWInt("DATG:ActiveJutsu", -1) == bind) and bind != 0)
        local isActiveJutsuKen = ((ply:GetNWInt("DATG:ActiveKenjutsu", -1) == i) and isKenjutsuMod and i <= 3)

        if isKenjutsuMod then

            if i <= 3 then
                print(ply:GetNWInt("DATG:ActiveKenjutsu", -1), bindken)
                if isActiveJutsuKen then
                    y[i] = Lerp(FrameTime() * 2, y[i] or SH(1040), (IsFightMode and SH(968) or SH(1040)))
                else
                    y[i] = Lerp(FrameTime() * 2, y[i] or SH(1040), (IsFightMode and SH(974) or SH(1040)))
                end
            end



            x[1] = Lerp(FrameTime() * 2, x[1] or SW(800), SW(800))
            x[2] = Lerp(FrameTime() * 2, x[2] or SW(930), SW(925))
            x[3] = Lerp(FrameTime() * 2, x[3] or SW(1050), SW(1050))

            isActiveJutsu = 0;
            x[4] = SW(666) + ((6) * SW(98))
            x[5] = SW(666) + ((7) * SW(98))
            x[6] = SW(666) + ((8) * SW(98))



            -- y[1] = Lerp(FrameTime() * 2, y[1] or SH(1040), (IsFightMode and SH(974) or SH(1040)))
            -- y[2] = Lerp(FrameTime() * 2, y[2] or SH(1040), (IsFightMode and SH(974) or SH(1040)))
            -- y[3] = Lerp(FrameTime() * 2, y[3] or SH(1040), (IsFightMode and SH(974) or SH(1040)))
            y[4] = Lerp(FrameTime() * 3, y[4] or SH(1040), SH(1200))
            y[5] = Lerp(FrameTime() * 3, y[5] or SH(1040), SH(1200))
            y[6] = Lerp(FrameTime() * 3, y[6] or SH(1040), SH(1200))
        else
            if isActiveJutsu then
                y[i] = Lerp(FrameTime() * 4, y[i] or SH(1040), (IsFightMode and SH(968) or SH(1040)))
            else
                y[i] = Lerp(FrameTime() * 4, y[i] or SH(1040), (IsFightMode and SH(974) or SH(1040)))
            end

            isActiveJutsuKen = 0;

            x[i] = Lerp(FrameTime() * 4, x[i] or (SW(666) + ((i - 1) * SW(98))), SW(666) + ((i - 1) * SW(98)))
        end




        local y = y[i]

        local is_complete = false
        if (isKenjutsuMod) then
            is_complete = (CD_KEN[i] == nil and true or CD_KEN[i].cur < CurTime())
        else
            is_complete = (CD[bind] == nil and true or CD[bind].cur < CurTime())
        end


        if is_complete then
            if isActiveJutsu then
                surface.SetMaterial(ATG_V2:Material("hud/bar2_1.png", "smooth mips"))
                surface.SetDrawColor(color_white)
                surface.DrawTexturedRectRotated(x[i] + SW(31), y + SH(44), SW(63), SH(63), rotateJutsu)
                rotateJutsu = rotateJutsu + 1
                if rotateJutsu > 360 then
                    rotateJutsu = 0;
                end
            else
                surface.SetMaterial(ATG_V2:Material("hud/bar2_1.png", "smooth mips"))
                surface.SetDrawColor(color_white)
                surface.DrawTexturedRect(x[i], y + SH(12), SW(63), SH(63))
            end
        else
            surface.SetMaterial(ATG_V2:Material("hud/bar2_0.png", "smooth mips"))
            surface.SetDrawColor(255, 255, 255, 100)
            surface.DrawTexturedRect(x[i], y + SH(12), SW(63), SH(63))

            masks.Start()
            surface.SetMaterial(ATG_V2:Material("hud/bar2_0.png", "smooth mips"))
            surface.SetDrawColor(color_white)
            surface.DrawTexturedRect(x[i], y + SH(12), SW(63), SH(63))
            masks.Source()
            if isKenjutsuMod and i <= 3 then
                DrawCircle(x[i] + SW(31) + SW(1) * 0.6, y + SH(43) + SH(1) * 0.2, SH(32),
                    (360 - (((CD_KEN[i].cur - CurTime()) / CD_KEN[i].cd) * 361)))
            else
                DrawCircle(x[i] + SW(31) + SW(1) * 0.6, y + SH(43) + SH(1) * 0.2, SH(32),
                    (360 - (((CD[bind].cur - CurTime()) / CD[bind].cd) * 361)))
            end
            masks.End()
        end

        local voicemode = ply:GetVoiceMode()
        for i = 1, 3 do
            surface.SetMaterial(ATG_V2:Material("hud/voix_" .. i .. ".png", "smooth noclamp"))
            surface.SetDrawColor(voicemode == i and color_white or Color(200, 200, 200))
            surface.DrawTexturedRect(SW(420) + i * SW(30), SH(1000), SW(16), SH(14))
        end
 

        local jutsuTech = (isKenjutsuMod and DATG.Kenjutsu[ply.Kenjutsu.sClass].Technics[i] or DATG.Technics[bind])
        -- local id = bind;
        -- if id ~= 0 then 
        --     ATG_V2:GetConfigJutsu(id, function(config)

        --         if id != bind then return end
                
        --         local level = LocalPlayer().ATG_Jutsu and (LocalPlayer().ATG_Jutsu[id] or 0) or 0
        --         local jutsuConfig = ATG_V2:GetConfigJutsuLevel(config, level)
 
        --         if jutsuConfig["tChakra"] == nil then
        --             print("Aucune configuration tChakra trouvée pour ce niveau (peut-être mauvais level ?)")
        --             return
        --         end
        --         print("Chakra requis :", jutsuConfig["tChakra"])

        --     end)
        -- end


        if jutsuTech != nil then
            if tSharinganSpell and (bind == 123) then
                -- surface.SetMaterial(ATG_V2:Material("hud/attack_circle.png"))
                -- surface.SetDrawColor((ply:GetNWInt("DATG:ActiveJutsu", -1) == 123 and Color(8, 8, 8, alpha) or Color(255, 255, 255, alpha)))
                -- surface.DrawTexturedRect(SW(666) + ((i-1) * SW(98)), y, SW(98), SH(98))
                jutsuTech = tSharinganSpell
            end

            if jutsuTech.Icon != "" then
                masks.Start()
                surface.SetMaterial(util.ImgurToMaterial(jutsuTech.Icon, "smooth mips"))
                if isKenjutsuMod then
                    surface.SetDrawColor(255, 255, 255,
                        ((CD_KEN[i] != nil and CD_KEN[i].cur >= CurTime() or false) and 100 or (ply:HasKenjutsu(i) and 255 or 150)))
                else
                    surface.SetDrawColor(255, 255, 255, (CD[bind] != nil and CD[bind].cur >= CurTime() and 100 or alpha))
                end
                surface.DrawTexturedRect(x[i] - SW(1), y + SH(11), SW(65), SH(65))
                masks.Source()
                DrawCircle(x[i] + SW(31) + SW(1) * 0.6, y + SH(43) + SH(1) * 0.2, SH(27), 361)
                masks.End()

                if isKenjutsuMod then
                    if CD_KEN[i] != nil and CD_KEN[i].cur >= CurTime() then
                        draw.SimpleText(math.floor((CD_KEN[i].cur - CurTime())) .. "s", ATG_V2.Fonts("ATG", 8, 500),
                            x[i] + SW(34), y + SH(30), color_white, TEXT_ALIGN_CENTER)
                    end
                else
                    if CD[bind] != nil and CD[bind].cur >= CurTime() then
                        draw.SimpleText(math.floor((CD[bind].cur - CurTime())) .. "s", ATG_V2.Fonts("ATG", 8, 500),
                            x[i] + SW(34), y + SH(30), color_white, TEXT_ALIGN_CENTER)
                    end
                end
            else
                DrawCircle(x[i] + SW(31) + SW(1) * 0.6, y + SH(43) + SH(1) * 0.2, SH(27), 365)
            end
        else
            DrawCircle(x[i] + SW(31) + SW(1) * 0.6, y + SH(43) + SH(1) * 0.2, SH(27), 361, nil, Color(75, 75, 75, 210))
        end

        if isActiveJutsu then
            surface.SetMaterial(ATG_V2:Material("hud/glow.png"))
            surface.SetDrawColor(255, 255, 255)
            surface.DrawTexturedRect(x[i], y + SH(11), SW(65), SH(67))
        end

        if isActiveJutsuKen then
            surface.SetMaterial(ATG_V2:Material("hud/glow.png"))
            surface.SetDrawColor(255, 255, 255)
            surface.DrawTexturedRect(x[i], y + SH(11), SW(65), SH(67))
        end
        
        if IsFightMode then
            surface.SetMaterial(ATG_V2:Material("hud/key2.png"))
            surface.SetDrawColor((input.IsKeyDown(DATG.Binds["Jutsu" .. i .. "_Key"]:GetInt()) and Color(200, 200, 200, alpha) or Color(255, 255, 255, alpha)))
            surface.DrawTexturedRect(x[i] + SW(12), y + SH(63), SW(40), SH(40))
            local key = "?"
            if isKenjutsuMod and i <= 3 then
                key = (DATG.Binds["Kenjutsu" .. i .. "_Key"]:GetInt() == 0 and "?" or input.GetKeyName(DATG.Binds["Kenjutsu" .. i .. "_Key"]:GetInt()))
            else
                key = (DATG.Binds["Jutsu" .. i .. "_Key"]:GetInt() == 0 and "?" or input.GetKeyName(DATG.Binds["Jutsu" .. i .. "_Key"]:GetInt()))
            end
            draw.SimpleText(key:upper(), ATG_V2.Fonts("ATG", 6, 500), x[i] + SW(31) - SW(1) * 0.2, y + SH(68),
                ((isActiveJutsu or isActiveJutsuKen) and color_white or Color(104, 104, 104)), TEXT_ALIGN_CENTER)

            if not isKenjutsuMod and ply.ATG_Jutsu != nil then
                local techid = DATG.Binds["Jutsu" .. i]:GetInt()
                if DATG.Technics[techid] != nil then
                    local chakraCost = getChakraAmount(techid, (LocalPlayer().ATG_Jutsu[techid] or 1))
                    draw.SimpleText(chakraCost, ATG_V2.Fonts("ATG", 7, 500), x[i] + SW(31) - SW(1) * 0.2, y - SH(5),
                    (isActiveJutsu and color_white or Color(200, 200, 200, 200)), TEXT_ALIGN_CENTER)
                end
            end
        end
    end

    -- for i, v in ipairs(CHAKRA_DISPLAY) do
    --     if v.cur < CurTime() then
    --         CHAKRA_DISPLAY[i] = nil;
    --         CHAKRA_DISPLAY = table.ClearKeys(CHAKRA_DISPLAY)
    --     end

    --     local y = SH(980) - (i - 1) * SH(19)

    --     local text = "-" ..
    --         v.chakra .. " (-" .. math.Round((v.chakra / ply:GetNWInt("CTG_Chakra:MaxChakra", 0)) * 100, 1) .. "%)"
    --     surface.SetFont(ATG_V2.Fonts("ATG", 8, 500))
    --     local w, h = surface.GetTextSize(text)

    --     draw.SimpleText(text, ATG_V2.Fonts("ATG", 8, 500), SW(600), y, Color(70, 174, 241), TEXT_ALIGN_RIGHT)

    --     surface.SetMaterial(ATG_V2:Material("hud/chakra.png"))
    --     surface.SetDrawColor(255, 255, 255, 255)
    --     surface.DrawTexturedRect(SW(599) - w - SW(17), y + SH(1), SW(9), SH(14))
    -- end

    for k, v in pairs(ents.FindInSphere(LocalPlayer():GetPos(), 300)) do
        if ! v:GetNWBool("CUse", false) then continue end
        local Pos = v:LocalToWorld(v:OBBCenter())
        Pos = Pos:ToScreen()

        local w = 200
        local distance = v:GetPos():Distance(LocalPlayer():GetPos())

        draw.RoundedBox(12, Pos.x - w / 2, Pos.y - SH(5), w, 30, Color(35, 37, 42, 255 - (distance / 500) * 255))
        draw.SimpleText("Interragir avec le menu contextuel.", ATG_V2.Fonts("ATG", math.Round(math.Clamp((distance - 500), 0.4, 1) * 12), 500), Pos.x, Pos.y,
            Color(255, 255, 255), TEXT_ALIGN_CENTER)
    end

    if GetConVar("see_bone"):GetString() == "1" then
        for i = 1, LocalPlayer():GetBoneCount() - 1 do
            local bone, __ = LocalPlayer():GetBonePosition(i)

            if bone == -1 then return end

            local pos = bone:ToScreen()

            draw.SimpleText(LocalPlayer():GetBoneName(i), "Default", pos.x - 5, pos.y, color_white)
        end

        for k, v in pairs(LocalPlayer():GetChildren()) do
            local bones = v:GetBoneCount() - 1
            for i = 1, bones do
                local bone, __ = v:GetBonePosition(i)

                if bone == -1 then return end

                local pos = bone:ToScreen()

                draw.SimpleText(v:GetBoneName(i), "Default", pos.x - 5, pos.y, color_white)
            end
        end
    end


    if IsFightMode then
        local boneId = LocalPlayer():LookupBone("ValveBiped.Bip01_Head1")
        if boneId == nil then return end
        local pos = LocalPlayer():GetBonePosition(boneId)
        if not pos then return end
        if pos == LocalPlayer():GetPos() then
            pos = LocalPlayer():GetBoneMatrix(boneId):GetTranslation()
        end
        pos = pos:ToScreen()
        if localpos.x == 0 then
            localpos = pos
        end
        localpos.x = Lerp(FrameTime() * 10, localpos.x, pos.x)
        localpos.y = Lerp(FrameTime() * 10, localpos.y, pos.y)

        if LocalPlayer().LastAttack != nil and LocalPlayer().LastAttack + 1 >= CurTime() then
            surface.SetMaterial(ATG_V2:Material("hud/circle_attack.png"))
            surface.SetDrawColor(200, 200, 200, 50)
            surface.DrawTexturedRect(localpos.x + SW(46), localpos.y - SH(110), SW(64), SH(64))
            surface.SetMaterial(ATG_V2:Material("hud/circle_attack.png"))
            surface.SetDrawColor(200, 200, 200, 50)
            surface.DrawTexturedRect(localpos.x + SW(48), localpos.y - SH(113), SW(64), SH(64))

            masks.Start()
            surface.SetMaterial(ATG_V2:Material("hud/circle_attack.png"))
            surface.SetDrawColor(Color(245, 143, 72))
            surface.DrawTexturedRect(localpos.x + SW(46), localpos.y - SH(110), SW(64), SH(64))
            masks.Source()
            DrawCircle(localpos.x + SW(80), localpos.y - SH(80), SH(44),
                math.Remap((LocalPlayer().LastAttack + 1) - CurTime(), 1, 0, 0, 1) * 360)
            masks.End()
        end
    end
end)

net.Receive("ATG:Jutsu:Cooldown", function()
    local jutsu = net.ReadUInt(8)
    local cd = net.ReadUInt(8)

    local ply = LocalPlayer()
    local isKenjutsuMod = (ply.Kenjutsu != nil and ply.Kenjutsu.sClass != nil and ply:GetNetworkVar("KenjutsuOut", false))

    if isKenjutsuMod then
        CD_KEN[jutsu] = { cur = CurTime() + cd, cd = cd }
    else

        CD[jutsu] = { cur = CurTime() + cd, cd = cd }
    end
end)

net.Receive("ATG:Deathscreen:OnDeath", function()
    local target = net.ReadPlayer()

    if not IsValid(target) then return end

    surface.PlaySound("atg_v2/other/deathscreen.mp3")

    local cur = CurTime() + 10

    local dpanel = vgui.Create("DFrame")
    dpanel:SetSize(SW(1920), SH(1080))
    dpanel:Center()
    dpanel:MakePopup()
    dpanel:SetKeyboardInputEnabled(true)
    dpanel:ShowCloseButton(false)
    dpanel:SetDraggable(false)
    dpanel.Paint = function(self, w, h)
    
        if cur-7 < CurTime() then
            if LocalPlayer():Alive() then
                self:Remove()
                return
            end
        end
    
        local sin = math.abs(math.sin(CurTime() * 2))

        surface.SetMaterial(Material("vgui/gradient_down"))
        surface.SetDrawColor(255, 0, 0, 100 + sin * 30)
        surface.DrawTexturedRect(0, 0, SW(1920), SH(1080))

        surface.SetMaterial(Material("vgui/gradient_up"))
        surface.SetDrawColor(255, 0, 0, 100 + sin * 30)
        surface.DrawTexturedRect(0, 0, SW(1920), SH(1080))

        ATG_V2:DrawBlurNoPanel(10, 3)

        surface.SetMaterial(ATG_V2:Material("hud/die.png"))
        surface.SetDrawColor(color_white)
        surface.DrawTexturedRectRotated(SW(1920 / 2), SH(510 - (math.abs(math.sin(CurTime() * 4)) * 10)), SW(140),
            SH(140), math.sin(CurTime() * 2) * 4)

        draw.SimpleText("Vous avez été tué.", ATG_V2.Fonts("ATG", 13, 500), SW(1920 / 2), SH(600), color_white,
            TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
        draw.SimpleText("par " .. target:NickATG(), ATG_V2.Fonts("ATG", 11, 500), SW(1920 / 2), SH(630),
            Color(163, 163, 163, 220), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)

        -- draw.SimpleText("Appuyez sur une touche pour réapparaitre", ATG_V2.Fonts("ATG", 9, 500), SW(1920/2), SH(630), Color(200, 200, 200, 100), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)

        draw.SimpleText("VOUS", ATG_V2.Fonts("ATG", 10, 500), SW(400), SH(300), color_white, TEXT_ALIGN_CENTER)
        draw.SimpleText(target:NickATG():upper(), ATG_V2.Fonts("ATG", 10, 500), SW(1500), SH(300), color_white,
            TEXT_ALIGN_CENTER)

        draw.RoundedBox(8, SW(585), SH(800), SW(750), SH(4), color_white)
        draw.SimpleText("<", ATG_V2.Fonts("ATG", 12, 500), SW(579), SH(783), color_white)

        surface.SetMaterial(ATG_V2:Material("hud/mort.png"))
        surface.SetDrawColor(0, 0, 0)
        surface.DrawTexturedRect(SW(942), SH(788), SW(24), SH(24))
    end
    dpanel.OnRemove = function()
        LocalPlayer():ConCommand("stopsound")
    end

    local respawn = vgui.Create("DButton", dpanel)
    respawn:SetSize(SW(400), SH(45))
    respawn:SetPos(SW(760), SH(670))
    respawn:SetText("")
    respawn.Paint = function(self, w, h)
        local time = cur - CurTime()
        local text2 = (time <= 0 and "Appuyez sur le bouton pour réapparaitre" or "Vous pourrez réapparaitre")
        local text = (time <= 0 and "maintenant" or "dans " .. math.floor(time) .. " seconde(s)")

        draw.RoundedBox(2, 0, 0, w, h,
            (time <= 0 and Color(127, 133, 148, (self:IsHovered() and 200 or 255)) or Color(81, 86, 97, 200)))
        if time <= 0 then
            draw.SimpleText(text2, ATG_V2.Fonts("ATG", 9, 500), w / 2, h / 2, color_white, 1, 1)
        else
            draw.SimpleText(text2, ATG_V2.Fonts("ATG", 9, 500), w / 2, SH(2), color_white, 1)
            draw.SimpleText(text, ATG_V2.Fonts("ATG", 7, 500), w / 2, SH(21), color_white, 1)
        end
    end
    respawn.DoClick = function()
        local time = cur - CurTime()
        if time > 0 then return end
        surface.PlaySound("atg_v2/sadmin/button_click.mp3")
        if not IsValid(dpanel) then return end
        dpanel:AlphaTo(0, 0.1, 0, function()
            if IsValid(dpanel) then
                dpanel:Remove()
            end
        end)

        net.Start("ATG:Deathscreen:Respawn")
        net.SendToServer()
    end

    local modelpanel = vgui.Create("ATG.ModelPanel", dpanel)
    modelpanel:SetSize(SW(400), SH(800))
    modelpanel:SetPos(SW(200), SH(200))
    modelpanel:SetFOV(50)
    modelpanel:SetCursor("arrow")
    modelpanel:SetModel(LocalPlayer():GetModel())
    modelpanel:SetPlayerChildren(LocalPlayer())
    modelpanel.LayoutEntity = function(self, ent)
        ent:ResetSequence("nrp_ninjutsu_heal_aerial_d51nj2_shadowclone_idle_type02")
        ent:SetAngles(Angle(0, 135, 0))
        return false
    end

    local modelpanel = vgui.Create("ATG.ModelPanel", dpanel)
    modelpanel:SetSize(SW(400), SH(800))
    modelpanel:SetPos(SW(1320), SH(200))
    modelpanel:SetCursor("arrow")
    modelpanel:SetFOV(50)
    modelpanel:SetModel(target:GetModel())
    modelpanel:SetPlayerChildren(target)
    modelpanel.LayoutEntity = function(self, ent)
        ent:ResetSequence("nrp_ninjutsu_heal_aerial_d51nj2_shadowclone_idle_type02")
        ent:SetAngles(Angle(0, 25, 0))
        return false
    end
end)

net.Receive("ATG:Deathscreen:KillNotification", function()
    local target = net.ReadPlayer()

    surface.PlaySound("atg_v2/other/kill.mp3")

    HUD_JUTSU[#HUD_JUTSU + 1] = { name = "✕ Vous avez tué " .. target:NickATG(), cur = CurTime() + 5 }
end)

net.Receive("ATG:Jutsu:ResetCooldown", function()
    CD[LocalPlayer():GetNWInt("DATG:ActiveJutsu", -1)] = nil;
    CD_KEN[LocalPlayer():GetNWInt("DATG:ActiveKenjutsu", -1)] = nil;
end)

local ent_ui = {}
local dropdown_ent = nil;

function LerpColor(frame, c1, c2)
    local r = Lerp(frame, c1.r, c2.r)
    local g = Lerp(frame, c1.g, c2.g)
    local b = Lerp(frame, c1.b, c2.b)

    return Color(r, g, b)
end

local function clickAltPersonMenu(ent, x, y)
    if IsValid(dropdown_ent) then dropdown_ent:Remove() end

    local dropdown;
    
    if ent:IsPlayer() then
        if not LocalPlayer():AdminMode() and ent:AdminMode() then return end
    end

    local actions;
    if ent:IsPlayer() then
        if LocalPlayer():AdminMode() then
            actions = {
                ["Expluser du serveur"] = function(target)
                    net.Start("ATG:Admin:KickPlayer")
                    net.WriteEntity(target)
                    net.SendToServer()

                    dropdown:Remove()
                end,
                ["Bannir le joueurs 1d"] = function(target)
                    net.Start("ATG:Admin:BanPlayer")
                    net.WriteEntity(target)
                    net.WriteInt(1, 6)
                    net.SendToServer()

                    dropdown:Remove()
                end,
                ["Bannir le joueurs 30d"] = function(target)
                    net.Start("ATG:Admin:BanPlayer")
                    net.WriteEntity(target)
                    net.WriteInt(30, 6)
                    net.SendToServer()

                    dropdown:Remove()
                end,
                ["Bannir le joueurs Perm"] = function(target)
                    net.Start("ATG:Admin:BanPlayer")
                    net.WriteEntity(target)
                    net.WriteInt(2, 6)
                    net.SendToServer()

                    dropdown:Remove()
                end,
                ["Avertir le joueur"] = function(target)
                    net.Start("ATG:Admin:WarnPlayer")
                    net.WriteEntity(target)
                    net.SendToServer()

                    dropdown:Remove()
                end,
                ["Voir le profile"] = function(target)
                    surface.PlaySound("garrysmod/content_downloaded.wav")
                    gui.OpenURL("https://steamcommunity.com/profiles/" .. target:SteamID64())
                end
            }

            if LocalPlayer():IsAdmin() and LocalPlayer():IsSuperAdmin() then

                actions["Ouvrir l'inventaire"] = function(target)
                    net.Start("ATG:YOLTIX_Inventory:OpenInventory:Admin")
                    net.WriteEntity(target)
                    net.SendToServer()

                    dropdown:Remove()
                end
            end
        else
            actions = {
                ["Se présenter"] = function(target)
                    ATG_V2.Presentation:Present(target)
                end,
                [(ent:GetNetworkVar("pCarriedPlayer", nil) == LocalPlayer() and "Arrêter de se faire porter" or "Demander a se faire porter")] = function(
                    target)
                    if ent:GetNetworkVar("pCarriedPlayer", nil) == LocalPlayer() then
                        net.Start("ATG:StopCarry")
                        net.SendToServer()
                    else
                        net.Start("ATG:AskToBeCarried")
                        net.WriteEntity(target)
                        net.SendToServer()
                    end
                end,
                ["Report ce joueur"] = function(target)
                    ATG_V2.SAdmin:ShowReport(target)
                end,
                ["Voir le profile"] = function(target)
                    surface.PlaySound("garrysmod/content_downloaded.wav")
                    gui.OpenURL("https://steamcommunity.com/profiles/" .. target:SteamID64())
                end,
                
            }

            if LocalPlayer( ):GetSquadName( ) != "" then
                actions["Inviter dans votre escouade"] = function(target)

                    if ent:GetSquadName( ) != "" then
                        notification.AddLegacy("Ce joueur est déjà dans une escouade.", NOTIFY_ERROR, 5)
                        return
                    end

                    if ent:GetSquadName( ) == LocalPlayer( ):GetSquadName( ) then
                        notification.AddLegacy("Ce joueur est déjà dans votre escouade.", NOTIFY_ERROR, 5)
                        return
                    end

                    net.Start( "Squad.SendInvitation" )
                    net.WriteEntity( ent )
                    net.SendToServer( )

                    notification.AddLegacy("Invitation envoyée à " .. ent:NickATG( ) .. ".", NOTIFY_GENERIC, 5)
                end
            end

            if (LocalPlayer( ):IsPolice() and LocalPlayer( ):HasPermision("amande")) then
                actions["Mettre une amende"] = function(target)
                    ATG_V2.Police:OpenAmmandeMenu(target)
                end
            end
        end
    else
        if ent:GetNWBool("CUse", false) then
            actions = {
                ["Interragir"] = function(ent)
                    ent:CInteract()
                end,
                ["Fermer"] = function(ent, drop)
                    if IsValid(drop) then
                        drop:Remove()
                    end
                end,
            }
        else
            actions = {
                ["Supprimer"] = function(ent)
                    net.Start("ATG_V2:Interaction:DeleteProp")
                    net.WriteEntity(ent)
                    net.SendToServer()
                end,
                ["Copier le owner"] = function(ent)
                    local target = ent:CPPIGetOwner()
                    if IsValid(target) then
                        SetClipboardText(target:SteamID64())
                    end
                end
            }
        end
    end

    local height = ((SH(33) * table.Count(actions)) + SH(30))

    local cur = CurTime()

    dropdown = vgui.Create("DPanel")
    dropdown:SetSize(SW(219), height)
    dropdown:SetPos(x, y)
    dropdown.Paint = function(self, w, h)
        draw.RoundedBox(4, 0, 0, w, h, Color(59, 59, 59, 200))
        if ent:IsPlayer() then
  
            draw.SimpleText(ent:NickATG(), ATG_V2.Fonts("ATG", 10, 500), w * 0.03, h * 0.005, color_white,
                TEXT_ALIGN_LEFT)
        else
            if ent:GetNWBool("CUse", false) then
                draw.SimpleText(ent:GetNWString("CName", "Interact"), ATG_V2.Fonts("ATG", 10, 500), w * 0.03, h * 0.005,
                    color_white, TEXT_ALIGN_LEFT)
            else
                if ent:CPPIGetOwner() == nil then
                    draw.SimpleText("Prop", ATG_V2.Fonts("ATG", 10, 500), w * 0.03, h * 0.005, color_white,
                        TEXT_ALIGN_LEFT)
                else
                    draw.SimpleText(ent:CPPIGetOwner():Nick(), ATG_V2.Fonts("ATG", 10, 500), w * 0.03, h * 0.005,
                        color_white, TEXT_ALIGN_LEFT)
                end
            end
        end
        if cur + 1 < CurTime() then
            if IsValid(self) then
                self:Remove()
            end
        end

        if IsValid(ent) then
            ent:SetRenderMode(RENDERMODE_TRANSALPHA)
            ent:SetColor(Color(255, 255, 255, 200))
            if ent:GetPos():Distance(LocalPlayer():GetPos()) > 400 then
                if IsValid(self) then
                    self:Remove()
                end
            end
        else
            if IsValid(self) then
                self:Remove()
            end
        end
    end
    dropdown.OnRemove = function()
        if IsValid(ent) then
            ent:SetRenderMode(RENDERMODE_NORMAL)
            ent:SetColor(color_white)
        end
    end
    dropdown_ent = dropdown;

    local dscrollpanel = vgui.Create("DScrollPanel", dropdown)
    dscrollpanel:SetSize(SW(219), height)
    dscrollpanel:SetPos(0, SH(30))
    dscrollpanel:GetVBar():SetWide(0)

    for k, v in SortedPairs(actions, true) do
        local button = vgui.Create("DButton", dscrollpanel)
        button:Dock(TOP)
        button:SetSize(SW(219), SH(33))
        button:SetText("")
        button.LerpColor = Color(39, 39, 39)
        button.Paint = function(self, w, h)
            if self:IsHovered() then
                self.LerpColor = LerpColor(FrameTime() * 4, self.LerpColor, Color(201, 150, 84))
            else
                self.LerpColor = LerpColor(FrameTime() * 8, self.LerpColor, Color(39, 39, 39))
            end
            draw.RoundedBox(0, 0, 0, w, h, self.LerpColor)
            draw.SimpleText(k, ATG_V2.Fonts("ATG", 7, 500), w * 0.03, h * 0.5, color_white, TEXT_ALIGN_LEFT,
                TEXT_ALIGN_CENTER)
            if self:IsHovered() then
                cur = CurTime()
            end
        end
        button.DoClick = function()
            v(ent, dropdown)
        end
    end
end

local function addAltPersonMenu(ent)
    local blacklistedmodel = {
        ["models/atg/head/head1.mdl"] = true,
        ["models/atg/head/head2.mdl"] = true,
        ["models/weapons/w_Physics.mdl"] = true,
        ["models/weapons/c_arms_citizen.mdl"] = true,
        ["models/weapons/c_superphyscannon.mdl"] = true,
        ["models/MaxOfS2D/camera.mdl"] = true,
        ["models/weapons/v_pistol.mdl"] = true,
        ["models/weapons/w_pistol.mdl"] = true,
        ["models/weapons/c_arms.mdl"] = true,
        ["models/weapons/maranzosabilitysweps/w_mas_ninjakunai.mdl"] = true,
        ["models/weapons/maranzosabilitysweps/c_mas_ninjakunai.mdl"] = true,
        [""] = true,
        [" "] = true,
    }

    if ent:IsWeapon() then return end

    if IsValid(ent_ui[ent]) then return end
    if ! ent:IsPlayer() then
        if ent:GetModel() == nil or ! ent:GetModel():find(".mdl") then return end
        if ent:GetParent() != nil and ent:GetParent():IsPlayer() then return end
        if blacklistedmodel[ent:GetModel():Trim(" ")] then return end
        if ! LocalPlayer():IsSuperAdmin() and ent:GetOwner() != LocalPlayer() and not ent:GetNWBool("CUse", false) then return end
        -- print(ent:GetModel())
    end

    local min, max, screenMin, screenMax, corners, width, height, dbutton;
    local function updatePos()
        if not IsValid(ent) then return end

        min, max = ent:OBBMins(), ent:OBBMaxs()

        screenMin, screenMax = Vector(1 / 0, 1 / 0), Vector(-1 / 0, -1 / 0)

        corners = {
            Vector(min.x, min.y, min.z),
            Vector(min.x, min.y, max.z),
            Vector(min.x, max.y, min.z),
            Vector(min.x, max.y, max.z),
            Vector(max.x, min.y, min.z),
            Vector(max.x, min.y, max.z),
            Vector(max.x, max.y, min.z),
            Vector(max.x, max.y, max.z),
        }

        cam.Start3D()
        for _, corner in ipairs(corners) do
            local worldCorner = ent:LocalToWorld(corner)
            local screenCorner = worldCorner:ToScreen()

            screenMin.x = math.min(screenMin.x, screenCorner.x)
            screenMin.y = math.min(screenMin.y, screenCorner.y)
            screenMax.x = math.max(screenMax.x, screenCorner.x)
            screenMax.y = math.max(screenMax.y, screenCorner.y)
        end
        cam.End3D()


        width = screenMax.x - screenMin.x
        height = screenMax.y - screenMin.y

        dbutton:SetSize(width, height)
        dbutton:SetPos(screenMin.x, screenMin.y)
    end


    dbutton = vgui.Create("DButton")
    dbutton:SetText("")
    dbutton.Paint = function(self, w, h)
        -- draw.RoundedBox(0, 0, 0, w, h, Color(255, 255, 255, 100))
        if self:IsHovered() then
            if ent:IsPlayer() then
                draw.SimpleText(ent:NickATG(), ATG_V2.Fonts("ATG", 5, 500), w / 2, h / 2, color_white, 1, 1)
            else
                -- draw.SimpleText("Entity("..ent:EntIndex()..")", ATG_V2.Fonts("ATG", 5, 500), w/2, h/2, color_white, 1, 1)
            end
        end
    end
    dbutton.OnRemove = function()
        if IsValid(ent) then
            ent:SetRenderMode(RENDERMODE_NORMAL)
            ent:SetColor(color_white)
        end
    end
    dbutton.OnCursorEntered = function()
        if IsValid(ent) then
            ent:SetRenderMode(RENDERMODE_TRANSALPHA)
            ent:SetColor(Color(255, 255, 255, 200))
        end
        surface.PlaySound("garrysmod/ui_hover.wav")
    end
    dbutton.OnCursorExited = function()
        if IsValid(ent) then
            ent:SetRenderMode(RENDERMODE_NORMAL)
            ent:SetColor(color_white)
        end
    end
    dbutton.DoClick = function()
        if IsValid(ent) then
            clickAltPersonMenu(ent, screenMin.x + width / 2, screenMin.y + height / 2)
        end
    end
    dbutton.Think = function()
        if not IsValid(ent) then
            if IsValid(dbutton) then
                dbutton:Remove()
            end
        end

        if ent:IsPlayer() != nil then
            if ent:IsPlayer() then
                if isfunction(ent.Alive) == false or ! ent:Alive() then
                    if IsValid(dbutton) then
                        dbutton:Remove()
                    end
                end
            end
        end


        if IsValid(ent) then
            if ent:GetPos():Distance(LocalPlayer():GetPos()) > 400 then
                if IsValid(dbutton) then
                    dbutton:Remove()
                end
            end
        else
            if IsValid(dbutton) then
                dbutton:Remove()
            end
        end

        updatePos()
    end

    updatePos()

    ent_ui[ent] = dbutton;
end

concommand.Add("removepanel", function()
    timer.Simple(1, function()
        for k, v in pairs(vgui.GetAll()) do
            if v:GetName() == "ATG.ModelPanel" and IsValid(v) then
                v:Remove()
            end
        end
    end)
end)

local context_frame;
local function ContextMenu(bool)
    if not bool and IsValid(context_frame) then
        context_frame:Remove()
        return
    end
    if bool and IsValid(context_frame) then context_frame:Remove() end

    local button
    context_frame = vgui.Create("DScrollPanel")
    context_frame:SetSize(SW(55), SH(350))
    context_frame:SetPos(SW(20), SH(20))
    context_frame.OnRemove = function()
        if IsValid(button) then
            button:Remove()
        end
    end
    local vbar = context_frame:GetVBar()
    vbar:SetWide(0)

    local listdesktop = list.Get("DesktopWindows")

    local btn = vgui.Create("DButton", context_frame)
    btn:Dock(TOP)
    btn:DockMargin(0, 0, 0, SH(5))
    btn:SetSize(SW(55), SH(55))
    btn:SetText("")
    btn.Paint = function(self, w, h)
        surface.SetMaterial(ATG_V2:Material("hud/camera.png"))
        surface.SetDrawColor(255, 255, 255, (self:IsHovered() and 200 or 255))
        surface.DrawTexturedRect(0, 0, w, h)
    end
    btn.DoClick = function()
        if LocalPlayer().CameraATG == 4 then
            LocalPlayer().CameraATG = 1; return
        end
        RunConsoleCommand("simple_thirdperson_enabled", "0")
        LocalPlayer().CameraATG = LocalPlayer().CameraATG + 1;
    end

    if not LocalPlayer():AdminMode() and not LocalPlayer():IsSuperAdmin() then return end

    for k, v in pairs(listdesktop) do
        local btn = vgui.Create("DButton", context_frame)
        btn:Dock(TOP)
        btn:DockMargin(0, 0, 0, SH(5))
        btn:SetSize(SW(55), SH(55))
        btn:SetText("")
        btn.Paint = function(self, w, h)
            surface.SetMaterial(Material(v.icon, "smooth noclamp"))
            surface.SetDrawColor(255, 255, 255, (self:IsHovered() and 200 or 255))
            surface.DrawTexturedRect(0, 0, w, h)
        end
        btn.DoClick = function()
            v.init()
        end
    end

    if LocalPlayer():AdminMode() or LocalPlayer():IsSuperAdmin() then
        button = vgui.Create("DButton")
        button:SetSize(SW(200), SH(50))
        button:SetPos(SW(1700), SH(1010))
        button:SetText("")
        button.Lerp = Color(35, 37, 42)
        button.Paint = function(self, w, h)
            if self:IsHovered() then
                self.Lerp = LerpColor(FrameTime() * 2, self.Lerp, Color(202, 146, 73))
            else
                self.Lerp = LerpColor(FrameTime() * 4, self.Lerp, Color(35, 37, 42))
            end

            draw.RoundedBox(8, 0, 0, w, h, self.Lerp)
            draw.SimpleText("Menu administrateur", ATG_V2.Fonts("ATG", 8, 500), w / 2, h / 2, color_white,
                TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
        end
        button.DoClick = function()
            RunConsoleCommand("staff_menu")
        end
    end
end

function CTG_UpdateHUD()
    -- print("update hud")
    local ply = LocalPlayer()
    if ply.Cinematique then return end
    if IsValid(MODEL_PANEL) then
        MODEL_PANEL:Remove()
    end
end

timer.Create("UpdateModel", 50, 0, function()
    CTG_UpdateHUD()
end)

local whitelistclass = {
    ["prop_physics"] = true
}


local toHide = {
    ["CHudHealth"] = true,
    ["CHudBattery"] = true,
    ["CHudAmmo"] = true,
    ["CHudSecondaryAmmo"] = true,
    ["CHudWeaponSelection"] = true,
    ["CHudDamageIndicator"] = true,
    ["DarkRP_HUD"] = false,
    ["DarkRP_EntityDisplay"] = false,
    ["CHudCrosshair"] = true,
    ["DarkRP_LocalPlayerHUD"] = true,
    ["DarkRP_Hungermod"] = true,
    ["DarkRP_Agenda"] = true,
    ["DarkRP_LockdownHUD"] = true,
    ["DarkRP_ArrestedHUD"] = true,
    ["DarkRP_ChatReceivers"] = false,
}

hook.Add("HUDShouldDraw", "ATG:HideOtherHuds", function(name)
    if toHide[name] then
        return false
    end
    return true
end)

hook.Add("OnContextMenuOpen", "ATG:Diesel", function()
    gui.EnableScreenClicker(true)
    ALT_MODE = true;

    ContextMenu(true)

    for k, v in pairs(ents.FindInSphere(LocalPlayer():GetPos(), 400)) do
        if (whitelistclass[v:GetClass()] == nil and ! IsValid(v:GetOwner())) and (! v:IsPlayer()) and (! v:GetNWBool("CUse", false)) then
            continue
        end

        if v == LocalPlayer() then continue end

        addAltPersonMenu(v)
    end
end)

hook.Add("OnContextMenuClose", "ATG:Diesel", function()
    gui.EnableScreenClicker(false)
    ALT_MODE = false;

    ContextMenu(false)


    for _, v in pairs(ent_ui) do
        if IsValid(v) then
            v:Remove()
        end
    end

    if IsValid(dropdown_ent) then
        dropdown_ent:Remove()
    end
end)

hook.Add("ContextMenuOpen", "ATG:Diesel", function()
    return false;
end)

hook.Add("InitPostEntity", "RemoveChatBubble", function()
    hook.Remove("StartChat", "StartChatIndicator")
    hook.Remove("FinishChat", "EndChatIndicator")

    hook.Remove("PostPlayerDraw", "DarkRP_ChatIndicator")
    hook.Remove("CreateClientsideRagdoll", "DarkRP_ChatIndicator")
    hook.Remove("player_disconnect", "DarkRP_ChatIndicator")
end)

hook.Add("KeyPress", "Diesel:ATG:Combo", function(ply, key)
    if (key == IN_ATTACK) then
        if ply.LastAttack != nil and ply.LastAttack + 1 > CurTime() then return end
        ply.LastAttack = CurTime()
    end
end)

-- hook.Add("PostDrawOpaqueRenderables", "Diesel:ATG:Voice", function()
--     if CurVoice > CurTime() then
--         local voice = DATG.Binds.Voice:GetInt()
--         render.StartWorldRings()
--         render.AddWorldRing(LocalPlayer():GetPos(), math.sqrt(ATG_V2.Voice.Config["Radius"][voice].range), 10, 50)
--         render.FinishWorldRings(ATG_V2.Voice.Config["Radius"][voice].color)
--     end
-- end)

hook.Add("PlayerButtonDown", "Diesel:ATG:Jutsu", function(ply, button)
    if CLIENT and not IsFirstTimePredicted() then
        return
    end

    if KEYS["Faire un ticket"] != nil and button == KEYS["Faire un ticket"](ply) then
        ATG_V2.SAdmin:ShowReport(nil)
        return
    end

    if KEYS["Activer/Désactiver votre pupille"] != nil and button == KEYS["Activer/Désactiver votre pupille"](ply) then
        net.Start("ATG:Jutsu:Dojutsu")
        net.SendToServer()
        return
    end

    if not ply:Alive() then return end

    if not ply:GetActiveWeapon() then return end
    if not IsValid(ply:GetActiveWeapon()) then return end
    if not ply:GetActiveWeapon():GetClass() then return end
    local IsFightMode = ply:GetActiveWeapon():GetClass() == "atg_fightmode"
    local IsInKenjutsuMod = ply:GetNetworkVar("KenjutsuOut", false)

    if not IsInKenjutsuMod then
        if KEYS["Changer d'arbre de jutsu"] != nil and button == KEYS["Changer d'arbre de jutsu"](ply) then
            local newTree = DATG.Binds["Tree"]:GetInt() + 1
            if newTree == 4 then newTree = 1; end
            DATG:RetrieveBinds(ply:GetNWInt("CharacterID", 1), newTree)
            return
        end
    end

    if button == DATG.Binds.FightMode:GetInt() and IsFightMode then
        surface.PlaySound("atg_v2/other/mode_combat.mp3")
        return
    end

    if ply:GetActiveWeapon() == nil then return end
    if not IsValid(ply:GetActiveWeapon()) then return end
    if ply:GetActiveWeapon():GetClass() == nil then return end
    if ply:GetActiveWeapon():GetClass() != "atg_fightmode" then return end

    local addJutsu = function(tech)
        if IsInKenjutsuMod then return end
        if tech == nil then return end
        table.insert(HUD_JUTSU, { name = "Jutsu seléctionné : " .. tech.Name, cur = CurTime() + 1 })
        ply:EmitSound("atg_v2/other/select_jutsu.mp3", 30, 100, 1, CHAN_AUTO)
    end

    for i = 1, 6 do
        if button == DATG.Binds["Jutsu" .. i .. "_Key"]:GetInt() then
            local tech = DATG.Technics[DATG.Binds["Jutsu" .. i]:GetInt()]
            addJutsu(tech)
        end
    end

    local addKenjutsu = function(tech)
        if not IsInKenjutsuMod then return end
        if tech == nil then return end
        table.insert(HUD_JUTSU, { name = "Technique Kenjutsu : " .. tech.Name, cur = CurTime() + 1 })
        ply:EmitSound("atg_v2/other/select_jutsu.mp3", 30, 100, 1, CHAN_AUTO)
    end

    for i = 1, 3 do
        if button == DATG.Binds["Kenjutsu" .. i .. "_Key"]:GetInt() and IsInKenjutsuMod then
            local tech = DATG.Kenjutsu[ply.Kenjutsu.sClass].Technics[i]
            addKenjutsu(tech)
        end
    end
end)

surface.CreateFont("EnVIF", {
    font = "Ninja Naruto",
    size = ScreenScale(7),
    weight = 500
})

CTG_UpdateHUD()

local lerp = 0;
hook.Add("CalcView", "SpeedCamera", function(ply, pos, angles, fov)
    if LocalPlayer().CameraATG == nil then LocalPlayer().CameraATG = 1; end
    local cameraactive = (LocalPlayer().CameraATG)

    if LocalPlayer().OnEditMode then
        local camDir = yoltixCam.ang:Forward() * -yoltixCam.zoom

        return {
            origin = ply:GetPos() + yoltixCam.offset + camDir,
            angles = yoltixCam.ang,
            fov = fov,
            drawviewer = true
        }
    end
    
    if LocalPlayer():IsCarry() and LocalPlayer():CarryBy() != nil then
        local target = LocalPlayer():CarryBy()
        local vecHeadPos = target:GetBonePosition(target:LookupBone("ValveBiped.Bip01_Head1"))
        local vPos = vecHeadPos + (angles + target:EyeAngles()):Forward() * -100

        local tTrace = util.TraceHull({
            start = vecHeadPos,
            endpos = vPos,
            mins = -Vector(5, 5, 5),
            maxs = Vector(5, 5, 5),
            filter = function(ent) return ent:IsWorld() end
        })

        if tTrace.Hit then
            vPos = tTrace.HitPos
        end

        return {
            origin = vPos,
            angles = angles + target:EyeAngles(),
            fov = fov,
            drawviewer = true
        }
    end
            
    local scale = ply:GetModelScale() or 1

    local activeweapon = ply:GetActiveWeapon() or nil
    
    if IsValid(activeweapon) and activeweapon:GetClass() == "atg_freecam" then
        if not ply.angFreeCam then

            ply:SetEyeAngles(ply:GetAngles())
            ply.angFreecam = ply:EyeAngles()
            ply.iFovFreecam = 50
        end
        if ply.angFreeCam then

            local vecHeadPos = ply:GetBonePosition(ply:LookupBone("ValveBiped.Bip01_Head1"))
            local vPos = vecHeadPos + (angles + ply.angFreeCam):Forward() * -(ply.iFovFreeCam or 100)

            local tTrace = util.TraceHull({
                start = vecHeadPos,
                endpos = vPos,
                mins = -Vector(5, 5, 5),
                maxs = Vector(5, 5, 5),
                filter = function(ent) return ent:IsWorld() end
            })
        
            if tTrace.Hit then
                vPos = tTrace.HitPos
            end
            
            return {
                origin = vPos,
                angles = angles + ply.angFreecam,
                fov = ply.iFovFreecam,
                drawviewer = true
            }

        end
    end

    if ply:GetNetworkVar("ATG:YOLTIX:JUTSU:SUSANO_2", false) then
        local desiredPos = pos + angles:Up() * 500
        
        local trace = util.TraceLine({
            start = pos,
            endpos = desiredPos,
            filter = function(ent) return ent:IsWorld() end
        })
        
        local finalPos
        if trace.Hit then
            finalPos = trace.HitPos - angles:Forward() * 20
        else
            finalPos = desiredPos
        end
        
        local view = {
            origin = finalPos,
            angles = angles,
            fov = fov,
            drawviewer = true
        }
        return view
    end

    if ply:GetNetworkVar("ATG:YOLTIX:JUTSU:GOLEM", false) then
        local angEye = ply:EyeAngles()
        angEye.p = 0

        return {
            origin = ply:GetPos() + Vector(0, 0, 500) + angEye:Forward()*-300,
            angles = angAngles,
            fov = iFOV,
            drawviewer = true
        }
    end

    if ply:GetNetworkVar("EloignedCamera", false) then
        local desiredPos = pos + angles:Up() * 500
        local velocity = ply:GetVelocity()
        local absX, absY, absZ = math.abs(velocity.x), math.abs(velocity.y), math.abs(velocity.z)
        local speed = math.max(absX, absY, absZ)

        if speed < 400 or ! ply:IsSprinting() then
            lerp = Lerp(FrameTime() * 3, lerp, 2.6)
        else
            lerp = Lerp(FrameTime() * 3, lerp, 2.6)
        end

        local trace = util.TraceLine({
            start = pos,
            endpos = pos - (angles:Forward() * 90) - ((angles:Forward() * 90) * lerp) - ((angles:Forward() * 40) * scale),
            filter = function(ent) return ent:IsWorld() end
        })

        if trace.Hit then
            pos = pos - (angles:Forward() * trace.HitPos:Distance(pos))
        else
            pos = pos - (angles:Forward() * 100) - ((angles:Forward() * 60) * lerp) - ((angles:Forward() * 40) * scale)
        end

        -- print(scale)
        local lerp = math.Remap(scale, 0.4, 1, -9, 10) 
        lerp = lerp + 40

        local view = {
            origin = pos + angles:Up() * (lerp),
            angles = angles,
            fov = fov,
            drawviewer = true
        }

        return view
    end
    
    
    if cameraactive == 1 then
        local velocity = ply:GetVelocity()
        local absX, absY, absZ = math.abs(velocity.x), math.abs(velocity.y), math.abs(velocity.z)
        local speed = math.max(absX, absY, absZ)

        if speed < 400 or ! ply:IsSprinting() then
            lerp = Lerp(FrameTime() * 3, lerp, 0.3)
        else
            lerp = Lerp(FrameTime() * 3, lerp, 1.2)
        end

        if ply:GetNetworkVar("ATG:YOLTIX:JUTSU:SUSANO_2", false) then
            lerp = Lerp(FrameTime() * 3, lerp, 12)
        end
        --print(scale)

        local trace = util.TraceLine({
            start = pos,
            endpos = pos - (angles:Forward() * 90) - ((angles:Forward() * 90) * lerp) - ((angles:Forward() * 40) * scale),
            filter = function(ent) return ent:IsWorld() end
        })

        if trace.Hit then
            pos = pos - (angles:Forward() * trace.HitPos:Distance(pos))
        else
            pos = pos - (angles:Forward() * 100) - ((angles:Forward() * 60) * lerp) - ((angles:Forward() * 40) * scale)
        end

        -- print(scale)
        local lerp = math.Remap(scale, 0.4, 1, -9, 10) 
        if ply:GetNetworkVar("ATG:YOLTIX:JUTSU:SUSANO_2", false) then
            lerp = lerp + 150
        end

        local view = {
            origin = pos + angles:Up() * (lerp),
            angles = angles,
            fov = fov,
            drawviewer = true
        }

        return view
    elseif cameraactive == 2 then
        local velocity = ply:GetVelocity()
        local absX, absY, absZ = math.abs(velocity.x), math.abs(velocity.y), math.abs(velocity.z)
        local speed = math.max(absX, absY, absZ)

        if speed < 400 or ! ply:IsSprinting() then
            lerp = Lerp(FrameTime() * 3, lerp, 0)
        elseif ply:GetNetworkVar("ATG:YOLTIX:JUTSU:SUSANO_2", false) then
            lerp = Lerp(FrameTime() * 3, lerp, 5)
        else
            lerp = Lerp(FrameTime() * 3, lerp, 1.2)
        end

        local lerp2 = math.Remap(scale, 0.4, 1, 0.5, 1.5)

        local pos = pos - (angles:Forward() * 25) - ((angles:Forward() * 90) * lerp) - ((angles:Forward() * 45) * lerp2)


        local lerp3 = math.Remap(scale, 0.4, 1, -2, 1)
        -- print(scale)
        local view = {
            origin = pos + ((angles:Up() * 20) * lerp3) + angles:Right() * 15,
            angles = angles,
            fov = fov,
            drawviewer = true
        }

        return view
    elseif cameraactive == 3 then
        local velocity = ply:GetVelocity()
        local absX, absY, absZ = math.abs(velocity.x), math.abs(velocity.y), math.abs(velocity.z)
        local speed = math.max(absX, absY, absZ)

        if speed < 400 or ! ply:IsSprinting() then
            lerp = Lerp(FrameTime() * 3, lerp, 0)
        else
            lerp = Lerp(FrameTime() * 3, lerp, 1.2)
        end
        
        local lerp1 = math.Remap(scale, 0.4, 1, 0.1, 1.2)
        
        local pos = pos - (angles:Forward() * 40) - ((angles:Forward() * 90) * lerp) - ((angles:Forward() * 60) * lerp1)
        
        local lerp2 = math.Remap(scale, 0.4, 1, -2, 1.2)
        -- print(scale)
        local view = {
            origin = pos + ((angles:Up() * 14) * lerp2),
            angles = angles,
            fov = fov,
            drawviewer = true
        }

        return view
    elseif cameraactive == 4 then
    end
end)

concommand.Add("pluiedecoin", function(ply, cmd, args)
    local entity = ply
    local pos = entity:GetPos() + Vector(0, 0, -20)

    local emitter = ParticleEmitter(pos)

    for i = 1, 100 do
        local part = emitter:Add("particles/smokey", pos)
        if (part) then
            part:SetDieTime(5)
            part:SetStartAlpha(255)
            part:SetEndAlpha(0)
            part:SetStartSize(6)
            part:SetEndSize(0)
            part:SetGravity(LocalPlayer():GetForward() * -30)
            part:SetVelocity(LocalPlayer():GetForward() * -10)
        end
    end

    emitter:Finish()
end)


local iFadeLerp = 0
local iCinematicMode = 0
local ScrW, ScrH = ScrW(), ScrH()



hook.Add("DrawOverlay", "ATG:DrawCinematic", function()
    local pLocal = LocalPlayer()
    if not IsValid(pLocal) then return end

    local iBarTall = ScrH * 0.1

    iFadeLerp = Lerp(FrameTime() * 2, iFadeLerp, (iCinematicMode == 2) and 1 or 0)

    surface.SetDrawColor(0, 0, 0)

    surface.DrawRect(0, 0, ScrW, iBarTall * iFadeLerp)
    surface.DrawRect(0, ScrH + 1 - iBarTall * iFadeLerp, ScrW, iBarTall * iFadeLerp)
end)

hook.Add("PlayerButtonDown", "ATG:ToggleCinematic", function(pPlayer, iKey)
    if not IsFirstTimePredicted() then return end

    if iKey == DATG.Binds.CinematicMode:GetInt() then
        iCinematicMode = iCinematicMode + 1
        pPlayer.Cinematique = true

        if iCinematicMode > 2 then
            iCinematicMode = 0
            pPlayer.Cinematique = false
        end
    end
end)

net.Receive("ATG:Animations:PlayAnim", function()
    local ent = net.ReadEntity()
    local sequence = net.ReadString()
    local loop = net.ReadBool()
    local speed = net.ReadFloat()
    if not IsValid(ent) then return end

    local sequenceId = ent:LookupSequence(sequence)

    if sequenceId >= 0 then
        ent:AddVCDSequenceToGestureSlot(GESTURE_SLOT_CUSTOM, sequenceId, 0, ! loop)
        ent:SetPlaybackRate(speed)
        timer.Simple(ent:SequenceDuration(sequenceId), function()
            if not IsValid(ent) then return end
            ent:SetPlaybackRate(1)
        end)
    end
end)

net.Receive("ATG:Animations:ResetAnim", function()
    local ent = net.ReadPlayer()
    if not IsValid(ent) then return end

    ent:AnimResetGestureSlot(GESTURE_SLOT_CUSTOM)
end)



hook.Add( "HUDPaint", "NoHUD_voice", function()
	LocalPlayer().DRPIsTalking = false 
end)

hook.Add("Initialize", "No_Voice_Panel", function() 
	hook.Remove( "InitPostEntity", "CreateVoiceVGUI" )
end)

concommand.Add("getmyscale", function()
    print(LocalPlayer():GetModelScale())
end)

hook.Add("CreateMove", "Jutsu_AimOnTarget", function(cmd)
    local ply = LocalPlayer()
    if not IsValid(ply) or not ply:Alive() then return end
    if not LocalPlayer():GetNetworkVar("Spectate", false) then return end
    local jutsuAimTarget = LocalPlayer():GetNetworkVar("Spectate", nil)

    cmd:SetViewAngles(LerpAngle(FrameTime() * 10 , cmd:GetViewAngles(), (jutsuAimTarget:EyePos() - ply:EyePos()):Angle()))
end)