AddCSLuaFile()

ENT.Type = "anim"
ENT.Base = "base_gmodentity"
ENT.PrintName = "Uzumaki (Entité)"
ENT.Author = "YOLTIX"
ENT.Spawnable = false
ENT.AdminOnly = true
ENT.RenderGroup = RENDERGROUP_TRANSLUCENT

function ENT:SetupDataTables()
    self:NetworkVar("Entity", 0, "A")
    self:NetworkVar("Entity", 1, "B")
    self:NetworkVar("Int", 0, "LinkHealth")
    self:NetworkVar("Int", 1, "MaxDist")
    self:NetworkVar("Int", 2, "Degate")
end

function ENT:Initialize()
    if SERVER then
        self:SetModel("models/dav0r/hoverball.mdl")
        self:PhysicsInitStatic(SOLID_NONE)
        hook.Add("EntityTakeDamage", self, function(s, ent, dmg)
            if ent == self:GetA() or ent == self:GetB() then
                self:EntityTakeDamage(ent, dmg)

            end
        end)
        self:SetNoDraw(true)
    end
end

function ENT:EntityTakeDamage(ent, dmg)
    if ent.ignoreBounce then return end
    local owner = self:GetOwner()
    local other = ent == self:GetA() and self:GetB() or self:GetA()

    if not ent:IsPlayer() then return end

    dmg:ScaleDamage(1 - (self.Reduce / 100))
    other.ignoreBounce = true
    other:TakeDamageInfo(dmg)
    other.ignoreBounce = nil
    
    self:SetLinkHealth(self:GetLinkHealth() - dmg:GetDamage())
    if(self:GetLinkHealth() <= 0) then
        owner.uzumaki_heart:Remove()
        self:Remove()
    end
 
end

function ENT:Think()
    if not IsValid(self:GetA()) or not IsValid(self:GetB()) then return end
    local a, b = self:GetA():GetPos(), self:GetB():GetPos()
    if CLIENT then
        OrderVectors(a, b)
        self:SetRenderBoundsWS(a, b)
    else
        self:SetPos(a + (b - a) / 2 + Vector(0, 0, 32))
        for k, v in pairs({self:GetA(), self:GetB()}) do
            if not IsValid(v) or not v:IsPlayer() or not v:Alive() then
                self:GetOwner().uzumaki_heart:Remove()
                self:Remove()
                return
            end
        end
        if(self:GetA():GetPos():DistToSqr(self:GetB():GetPos()) > self:GetMaxDist()) then
            
            local owner = self:GetOwner()
            local other = self:GetA() == owner and self:GetB() or self:GetA()

            if IsValid(owner) and owner.uzumaki_heart and owner.uzumaki_heart[other] then
                owner.uzumaki_heart[other]:Remove()
                owner.uzumaki_heart[other] = nil
            end
        
            if owner.duo then
                owner.duo[other] = nil
            end

            self:Remove()
            self:GetA():TakeDamage(self:GetDegate(), self:GetOwner(), self:GetOwner())
            return
        end 
    end
end

if SERVER then 


	function ENT:Initialize()

		self:SetModel("models/hunter/blocks/cube025x2x025.mdl")
		self:PhysicsInit(SOLID_NONE)
		self:SetMaterial("Models/effects/vol_light001")
		self:DrawShadow(false)

		local phys = self:GetPhysicsObject()
		if (phys:IsValid()) then
			phys:Wake()
			phys:AddGameFlag(FVPHYSICS_NO_IMPACT_DMG)
			phys:EnableMotion(false)
			phys:EnableCollisions(true)
		end
	end
	




end

