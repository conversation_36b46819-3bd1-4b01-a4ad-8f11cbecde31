AddCSLuaFile()
ENT.Type = "anim"
ENT.Base = "base_gmodentity"

ENT.AutomaticFrameAdvance = true
ENT.PrintName = "Tissu spirituel"
ENT.Author = "ADEMO"
ENT.Category = "ATG - Farm"
ENT.Spawnable = true
ENT.AdminSpawnable = true

ENT.Model = "models/narutorp/sack.mdl"
ENT.ItemRarity = "rare"

function ENT:Initialize()
    self:SetModel(self.Model)
    self:PhysicsInit(SOLID_VPHYSICS)
    self:SetMoveType(MOVETYPE_VPHYSICS)
    self:SetSolid(SOLID_VPHYSICS)
    
    local phys = self:GetPhysicsObject()
    if phys:IsValid() then
        phys:Wake()
    end
end

function ENT:SetupDataTables()
    self:NetworkVar("Int", 0, "Amount")
    self:SetAmount(1)
end

function ENT:Think()
    self:FrameAdvance()
end
 
function ENT:Use(activator)
    if activator:IsPlayer() then
        if not activator:IsSuperAdmin() then 
            if activator:AdminMode() then 
                DarkRP.notify(activator, 1, 4, "Vous ne pouvez pas ramasser cet objet en mode admin.")
                return 
            end
        end

        self:Remove()
        YOLTIX_Inventory:AddInventory(activator, "tissu_spirituel", 1, activator:ATG_GetCurrentCharacter().id)
    end
end

if SERVER then return end

local rarityColors = {
    common = Color(255, 255, 255),
    uncommon = Color(0, 255, 0),
    rare = Color(0, 0, 255),
    legendary = Color(255, 165, 0)
}

local function IsLookingAt(ply, targetVec)
    local diff = targetVec - ply:GetShootPos()
    return ply:GetAimVector():Dot(diff) / diff:Length() >= 0.95 
end

function ENT:Draw()
    local range = math.cos(RealTime() * 4) * 16
    local pos = self:GetPos() + Vector(0, 0, 10)

    self:DrawModel()

    local ang = Angle(0, EyeAngles().y, 0)
    ang:RotateAroundAxis(ang:Forward(), 90)
    ang:RotateAroundAxis(ang:Right(), 90)

    local rarity_color = rarityColors[self.ItemRarity] or rarityColors.common

    render.SetMaterial(Material("sprites/light_glow02_add"))
    render.DrawSprite(pos, 42 + range, 42 + range, rarity_color)
    render.DrawSprite(pos, 16 + range, 16 + range, Color(255, 255, 255))

    if IsLookingAt(LocalPlayer(), pos) then
        ang = EyeAngles()
        ang:RotateAroundAxis(ang:Forward(), 90)
        ang:RotateAroundAxis(ang:Right(), 90)
        
        cam.Start3D2D(pos + EyeAngles():Right() * 32, ang, .15)
            draw.SimpleTextOutlined(
                self.PrintName,
                "DermaLarge",
                -8,
                8,
                Color(255, 255, 255),
                TEXT_ALIGN_CENTER,
                TEXT_ALIGN_CENTER,
                1,
                Color(0, 0, 0)
            )
        cam.End3D2D()
    end
end