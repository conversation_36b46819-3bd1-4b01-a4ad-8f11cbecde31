SWEP.PrintName = "Megaphone"
SWEP.Author = "Wasied"
SWEP.Category = "Wasied - EVoice"
SWEP.Purpose = ""

SWEP.Slot = 1
SWEP.SlotPos = 0
SWEP.Spawnable = true

SWEP.ViewModel = Model("models/sterling/c_enhanced_megaphone.mdl")
SWEP.WorldModel = Model("models/sterling/w_enhanced_megaphone.mdl")
SWEP.ViewModelFOV = 90
SWEP.DrawAmmo = false
SWEP.UseHands = true

SWEP.Primary.ClipSize = -1
SWEP.Primary.DefaultClip = -1
SWEP.Primary.Automatic = true
SWEP.Primary.Ammo = "none"

SWEP.Secondary.ClipSize = -1
SWEP.Secondary.DefaultClip = -1
SWEP.Secondary.Automatic = true
SWEP.Secondary.Ammo = "none"

-- Megaphone primary attack
function SWEP:PrimaryAttack()

    if not EVoice.Config.EnableMegaphone then return end

    local pOwner = self:GetOwner()
    if not IsValid(pOwner) then return end

    if (pOwner.iUseMegaphoneCooldown or 0) > CurTime() then return end
    pOwner.iUseMegaphoneCooldown = CurTime() + 0.5

    local eViewModel = pOwner:GetViewModel()
    local iSeqId

    if not pOwner.bIsMegaphoneTalking then
        iSeqId = eViewModel:LookupSequence("talk")
    else
        iSeqId = eViewModel:LookupSequence("notalk")
    end

    pOwner.bIsMegaphoneTalking = not pOwner.bIsMegaphoneTalking

    if SERVER and EVoice.Config.MegaphoneAnim then
        net.Start("EVoice:ShowMegaphoneDistance")
            net.WriteBool(pOwner.bIsMegaphoneTalking)
        net.Send(pOwner)
    end

    eViewModel:SendViewModelMatchingSequence(iSeqId)
    return false

end

-- Megaphone seconday attack
function SWEP:SecondaryAttack()
    return false
end

-- Automatically remove the megaphone when the player does not have in in hands
function SWEP:Holster()
    
    if not EVoice.Config.EnableMegaphone then return end

    if SERVER then

        local pOwner = self:GetOwner()
        if not IsValid(pOwner) then return end

        pOwner.bIsMegaphoneTalking = false

        net.Start("EVoice:ShowMegaphoneDistance")
            net.WriteBool(pOwner.bIsMegaphoneTalking)
        net.Send(pOwner)

    end

    return true

end