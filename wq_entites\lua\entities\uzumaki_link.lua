AddCSLuaFile()

ENT.Type = "anim"
ENT.Base = "base_gmodentity"
ENT.PrintName = "Chaine Link (Entité)"
ENT.Author = "YOLTIX"
ENT.Spawnable = false
ENT.AdminOnly = true

function ENT:SetupDataTables()
    self:NetworkVar("Entity", 0, "Target")

    self:NetworkVar("Int", 0, "Duration")
end

if CLIENT then
    function ENT:Initialize()
        if not IsValid(self:GetTarget()) or not IsValid(self:GetOwner()) then return end
        
        self.Particle = CreateParticleSystem(self, "[4]_chains_yellow", PATTACH_ABSORIGIN_FOLLOW,1, Vector(0, 0, 25))
        self.Particle2 = CreateParticleSystem(self, "[0]_chainstun_main_blue", PATTACH_ABSORIGIN_FOLLOW,1, Vector(0, 0, 25))
        

        if IsValid(self.Particle) then
            self.Particle:SetShouldDraw(true)
        end
        
        if IsValid(self.Particle2) then
            self.Particle2:SetShouldDraw(true)
        end
        
        timer.Simple(self:GetDuration(), function()
            if IsValid(self) then
                self:RemoveParticles()
            end
        end)
    end

    function ENT:Think()
        if not IsValid(self:GetTarget()) or not IsValid(self:GetOwner()) then return end
        
        local ownerBoneID = self:GetOwner():LookupBone("ValveBiped.Bip01_Spine2")
        local targetBoneID = self:GetTarget():LookupBone("ValveBiped.Bip01_Spine2")
        
        if not ownerBoneID or not targetBoneID then return end
        
        local ownerBonePos = self:GetOwner():GetBonePosition(ownerBoneID)
        local targetBonePos = self:GetTarget():GetBonePosition(targetBoneID)
        

        if IsValid(self.Particle) then
            self.Particle:SetControlPointEntity(0, self:GetOwner())
            self.Particle:SetControlPoint(1, targetBonePos)
        end
        
        if IsValid(self.Particle2) then
            self.Particle2:SetControlPointEntity(0, self:GetOwner())
            self.Particle2:SetControlPoint(1, targetBonePos)
        end
        
        self:NextThink(CurTime())
        return true
    end

    function ENT:RemoveParticles()
        if IsValid(self.Particle) then
            self.Particle:StopEmissionAndDestroyImmediately()
            self.Particle = nil
        end
        
        if IsValid(self.Particle2) then
            self.Particle2:StopEmissionAndDestroyImmediately()
            self.Particle2 = nil
        end
    end

    function ENT:OnRemove()
        self:RemoveParticles()
    end
end

if SERVER then 
    function ENT:Initialize()
        self:SetModel("models/hunter/blocks/cube025x2x025.mdl")
        self:PhysicsInit(SOLID_NONE)
        self:SetNoDraw(true)
        self:DrawShadow(false)
        
        local phys = self:GetPhysicsObject()
        if IsValid(phys) then
            phys:Wake()
            phys:EnableMotion(false)
            phys:EnableCollisions(false)
        end
        
        if IsValid(self:GetOwner()) then
            self:SetPos(self:GetOwner():GetPos())
            self:SetParent(self:GetOwner())
        end
    end
end