local language_code = "PT-BR"

AWarn.Localization:RegisterLanguage( language_code, "Português brasileiro" )

-- Credit: https://www.gmodstore.com/users/saigonosamurai

AWarn.Localization:AddDefinition( language_code, "welcome1", 					"Bem-vindo ao AWarn3!" )
AWarn.Localization:AddDefinition( language_code, "insufficientperms", 			"Você não tem permissão para executar este comando." )
AWarn.Localization:AddDefinition( language_code, "insufficientperms2", 			"Você não tem permissão para ver as advertências deste jogador." )
AWarn.Localization:AddDefinition( language_code, "commandnonexist", 			"Este comando não existe." )
AWarn.Localization:AddDefinition( language_code, "invalidtargetid", 			"ID ou alvo inválido." )
AWarn.Localization:AddDefinition( language_code, "invalidtarget", 				"Alvo inválido." )
AWarn.Localization:AddDefinition( language_code, "reasonrequired", 				"Um motivo para a advertência é necessário." )
AWarn.Localization:AddDefinition( language_code, "remove1activewarn", 			"Você removeu uma advertência ativa de" )
AWarn.Localization:AddDefinition( language_code, "deletedwarningid", 			"ID de advertência apagado" )
AWarn.Localization:AddDefinition( language_code, "removeallwarnings", 			"Você limpou as advertências de" )
AWarn.Localization:AddDefinition( language_code, "deletedwarningsfor", 			"Todas advertências apagadas para" )
AWarn.Localization:AddDefinition( language_code, "cantopenconsole", 			"Você não pode abrir o menu pelo console." )
AWarn.Localization:AddDefinition( language_code, "invalidoption", 				"Opção inválida." )
AWarn.Localization:AddDefinition( language_code, "invalidoptionvaluetype",		"Tipo de valor inválido para opção." )
AWarn.Localization:AddDefinition( language_code, "optionsloaded",				"Opções carregadas!" )
AWarn.Localization:AddDefinition( language_code, "nopunishment",				"Sem punição para esta quantidade de advertências." )
AWarn.Localization:AddDefinition( language_code, "punishmentsloaded",			"Punições carregadas!" )
AWarn.Localization:AddDefinition( language_code, "playernotallowedwarn",		"Este jogador não pode ser advertido." )
AWarn.Localization:AddDefinition( language_code, "warnmessage1",				"Você foi advertido por %s pelo motivo %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage2",				"Você advertiu %s por %s" )
AWarn.Localization:AddDefinition( language_code, "warnmessage3",				"%s foi advertido por %s pelo motivo %s" )
AWarn.Localization:AddDefinition( language_code, "warnmessage4",				"Você foi advertido por %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage5",				"Você foi advertido %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage6",				"%s foi advertido por %s." )
AWarn.Localization:AddDefinition( language_code, "joinmessage1",				"entrou no servidor com advertências." )
AWarn.Localization:AddDefinition( language_code, "joinmessage2",				"Sua última advertência foi em:" )
AWarn.Localization:AddDefinition( language_code, "joinmessage3",				"Bem vindo de volta! Parece que você foi advertido anteriormente." )
AWarn.Localization:AddDefinition( language_code, "joinmessage4",				"Você pode ver suas advertências digitando" )
AWarn.Localization:AddDefinition( language_code, "joinmessage5",				"Um jogador está entrando com advertências ativas: " )
AWarn.Localization:AddDefinition( language_code, "clientjoinmessage",			"Exibir contagem de advertências para o jogador ao entrar" )
AWarn.Localization:AddDefinition( language_code, "adminjoinmessage",			"Exibir mensagem aos administradores quando um jogador com advertências entrar" )
AWarn.Localization:AddDefinition( language_code, "closemenu",					"Fechar menu" )
AWarn.Localization:AddDefinition( language_code, "searchplayers",				"Procurar jogadores" )
AWarn.Localization:AddDefinition( language_code, "viewwarnings",				"Ver advertências" )
AWarn.Localization:AddDefinition( language_code, "configuration",				"Configuração" )
AWarn.Localization:AddDefinition( language_code, "clientoptions",				"Opções de usuário" )
AWarn.Localization:AddDefinition( language_code, "serveroptions",				"Opções do servidor" )
AWarn.Localization:AddDefinition( language_code, "colorcustomization",			"Customização de cores" )
AWarn.Localization:AddDefinition( language_code, "colorselection",				"Seleção de cores" )
AWarn.Localization:AddDefinition( language_code, "languageconfiguration",		"Customização de linguagem" )
AWarn.Localization:AddDefinition( language_code, "selectlanguage",				"Selecionar idioma" )
AWarn.Localization:AddDefinition( language_code, "enablekickpunish",			"Habilitar punição por kick" )
AWarn.Localization:AddDefinition( language_code, "enablebanpunish",				"Habilitar punição por ban" )
AWarn.Localization:AddDefinition( language_code, "enabledecay",					"Habilitar expiração de advertências ativas" )
AWarn.Localization:AddDefinition( language_code, "resetafterban",				"Expirar advertências ativas pós banimento" )
AWarn.Localization:AddDefinition( language_code, "allowwarnadmins",				"Habilitar advertências em admins" )
AWarn.Localization:AddDefinition( language_code, "pressenter",					"Enter para salvar mudanças" )
AWarn.Localization:AddDefinition( language_code, "entertosave",					"Enter para salvar" )
AWarn.Localization:AddDefinition( language_code, "chatprefix",					"Prefixo de chat" )
AWarn.Localization:AddDefinition( language_code, "warningdecayrate",			"Taxa de expiração de advertências (em minutos)" )
AWarn.Localization:AddDefinition( language_code, "serverlanguage",				"Idioma do servidor" )
AWarn.Localization:AddDefinition( language_code, "punishmentsconfiguration",	"Configuração de punições" )
AWarn.Localization:AddDefinition( language_code, "addpunishment",				"Adicionar punição" )
AWarn.Localization:AddDefinition( language_code, "warnings",					"Advertências" )
AWarn.Localization:AddDefinition( language_code, "punishtype",					"Tipo de punição" )
AWarn.Localization:AddDefinition( language_code, "punishlength",				"Duração da punição" )
AWarn.Localization:AddDefinition( language_code, "playermessage",				"Mensagem do jogador" )
AWarn.Localization:AddDefinition( language_code, "playername",					"Nome do jogador" )
AWarn.Localization:AddDefinition( language_code, "messagetoplayer",				"Mensagem para o jogador" )
AWarn.Localization:AddDefinition( language_code, "servermessage",				"Mensagem do servidor" )
AWarn.Localization:AddDefinition( language_code, "messagetoserver",				"Mensagem para o servidor" )
AWarn.Localization:AddDefinition( language_code, "deletewarning",				"Apagar advertência" )
AWarn.Localization:AddDefinition( language_code, "punishaddmenu",				"Menu de adição de punições" )
AWarn.Localization:AddDefinition( language_code, "inminutes",					"Em minutos" )
AWarn.Localization:AddDefinition( language_code, "0equalperma",					"0 = Permanente" )
AWarn.Localization:AddDefinition( language_code, "use%",						"Use %s para ver o nome do jogador" )
AWarn.Localization:AddDefinition( language_code, "setdefault",					"Usar padrão" )
AWarn.Localization:AddDefinition( language_code, "showingownwarnings",			"Mostrando suas advertências" )
AWarn.Localization:AddDefinition( language_code, "warnedby",					"Advertido por" )
AWarn.Localization:AddDefinition( language_code, "warningserver",				"Advertido em" )
AWarn.Localization:AddDefinition( language_code, "warningreason",				"Motivo da advertência" )
AWarn.Localization:AddDefinition( language_code, "warningdate",					"Data da advertência" )
AWarn.Localization:AddDefinition( language_code, "nothing",						"NADA" )
AWarn.Localization:AddDefinition( language_code, "submit",						"Enviar" )
AWarn.Localization:AddDefinition( language_code, "connectedplayers",			"Jogadores conectados" )
AWarn.Localization:AddDefinition( language_code, "displaywarningsfor",			"Mostrando advertências de" )
AWarn.Localization:AddDefinition( language_code, "activewarnings",				"Advertências ativas" )
AWarn.Localization:AddDefinition( language_code, "selectedplayernowarnings",	"Este jogador não possui advertências." )
AWarn.Localization:AddDefinition( language_code, "selectplayerseewarnings",		"Selecione um jogador para ver suas advertências." )
AWarn.Localization:AddDefinition( language_code, "warnplayer",					"Advertir jogador" )
AWarn.Localization:AddDefinition( language_code, "reduceactiveby1",				"Reduzir advertências ativas em 1" )
AWarn.Localization:AddDefinition( language_code, "playerwarningmenu",			"Player Warning Menu" )
AWarn.Localization:AddDefinition( language_code, "playersearchmenu",			"Menu de busca" )
AWarn.Localization:AddDefinition( language_code, "warningplayer",				"Jogador advertido" )
AWarn.Localization:AddDefinition( language_code, "excludeplayers",				"Excluir jogadores sem advertências" )
AWarn.Localization:AddDefinition( language_code, "searchforplayers",			"Procurar jogadores por nome ou SteamID64" )
AWarn.Localization:AddDefinition( language_code, "name",						"Nome" )
AWarn.Localization:AddDefinition( language_code, "lastplayed",					"Visto por último" )
AWarn.Localization:AddDefinition( language_code, "lastwarned",					"Última advertência" )
AWarn.Localization:AddDefinition( language_code, "never",						"Nunca" )
AWarn.Localization:AddDefinition( language_code, "playerid",					"ID do jogador" )
AWarn.Localization:AddDefinition( language_code, "lookupplayerwarnings",		"Procurar por advertências do jogador" )
AWarn.Localization:AddDefinition( language_code, "servername",					"Nome do servidor" )

AWarn.Localization:AddDefinition( language_code, "punishmentoptions",			"Punições" )
AWarn.Localization:AddDefinition( language_code, "kickpunishdescription",		"Se habilitado, o AWarn3 poderá expulsar jogadores como punição." )
AWarn.Localization:AddDefinition( language_code, "banpunishdescription",		"Se habilitado, o AWarn3 poderá banir jogadores como punição." )
AWarn.Localization:AddDefinition( language_code, "enabledecaydescription",		"Se habilitado, advertências ativas irão expirar com o tempo." )
AWarn.Localization:AddDefinition( language_code, "reasonrequireddescription",	"Se habilitado, administradores deverão fornecer uma razão para a advertência." )
AWarn.Localization:AddDefinition( language_code, "resetafterbandescription",	"Se habilitado, a users active warnings will reset to 0 após ser banido pelo AWarn3." )
AWarn.Localization:AddDefinition( language_code, "logevents",					"Registrar Eventos de Advertências." )
AWarn.Localization:AddDefinition( language_code, "logeventsdescription",		"Se habilitado, ações do AWarn3 serão registradas para um arquivo de texto." )
AWarn.Localization:AddDefinition( language_code, "allowwarnadminsdescription",	"Se habilitado, administradores poderão advertir outros administradores." )
AWarn.Localization:AddDefinition( language_code, "clientjoinmessagedescription","Se habilitado, jogadores, ao entrar no servidor, serão avisados sobre suas advertências." )
AWarn.Localization:AddDefinition( language_code, "adminjoinmessagedescription",	"Se habilitado, administradores no servidor verão quando algum jogador com advertências entrar." )
AWarn.Localization:AddDefinition( language_code, "chatprefixdescription",		"O comando padrão do AWarn3 usado para o menu de comandos. Padrão: !warn" )
AWarn.Localization:AddDefinition( language_code, "warningdecayratedescription",	"O tempo (em minutos) que um jogador precisa estar conectado para que uma advertência ativa expire." )
AWarn.Localization:AddDefinition( language_code, "servernamedescription",		"O nome deste servidor. Isto é útil para configurações múltiplas de servidores" )
AWarn.Localization:AddDefinition( language_code, "selectlanguagedescription",	"Este é o idioma em que as mensagens serão exibidas no servidor." )
AWarn.Localization:AddDefinition( language_code, "theme",						"Tema da Interface" )
AWarn.Localization:AddDefinition( language_code, "themeselect",					"Selecione o Tema" )
AWarn.Localization:AddDefinition( language_code, "punishgroup",					"Grupo de Punição" )
AWarn.Localization:AddDefinition( language_code, "grouptoset",					"Grupo para Definir" )
AWarn.Localization:AddDefinition( language_code, "viewnotes",					"Ver Notas de Jogador" )
AWarn.Localization:AddDefinition( language_code, "playernotes",					"Notas do Jogador" )

AWarn.Localization:AddDefinition( language_code, "interfacecustomizations",		"Customizações da Interface" )
AWarn.Localization:AddDefinition( language_code, "enableblur",					"Habilitar desfoque do fundo" )
AWarn.Localization:AddDefinition( language_code, "chooseapreset",				"Selecionar predefinição (Opcional)" )
AWarn.Localization:AddDefinition( language_code, "warningpresets",				"Predefinições" )
AWarn.Localization:AddDefinition( language_code, "addeditpreset",				"Adicionar/Editar predefinições" )
AWarn.Localization:AddDefinition( language_code, "presetname",					"Nome da predefinição" )
AWarn.Localization:AddDefinition( language_code, "presetreason",				"Razão da predefinição" )
