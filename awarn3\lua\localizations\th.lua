local language_code = "TH-th"

--[[Thai translation by Kaname]]--
--https://steamcommunity.com/id/KanameA/

AWarn.Localization:RegisterLanguage( language_code, "Thai" )

AWarn.Localization:AddDefinition( language_code, "welcome1", 					"ยินดีต้อนรับสู่ AWarn3!" )
AWarn.Localization:AddDefinition( language_code, "insufficientperms", 			"คุณไม่มีสิทธิ์ที่จะใช้คำสั่งนี้" )
AWarn.Localization:AddDefinition( language_code, "insufficientperms2", 			"คุณไม่มีสิทธิ์ที่จะดูคำเตือนของผู้เล่นคนนี้" )
AWarn.Localization:AddDefinition( language_code, "commandnonexist", 			"ไม่พบคำสั่ง" )
AWarn.Localization:AddDefinition( language_code, "invalidtargetid", 			"ไม่พบผู้เล่น หรือ ID" )
AWarn.Localization:AddDefinition( language_code, "invalidtarget", 				"ไม่พบผู้เล่น" )
AWarn.Localization:AddDefinition( language_code, "reasonrequired", 				"การตักเตือนจำเป็นต้องใส่เหตุผล" )
AWarn.Localization:AddDefinition( language_code, "remove1activewarn", 			"คุณได้ทำการลบ 1 คำเตือนออกจาก" )
AWarn.Localization:AddDefinition( language_code, "deletedwarningid", 			"ลบคำเตือนจาก ID" )
AWarn.Localization:AddDefinition( language_code, "removeallwarnings", 			"คุณได้ทำการลบคำเตือนทั้งหมดออกจาก" )
AWarn.Localization:AddDefinition( language_code, "deletedwarningsfor", 			"ลบคำเตือนทำหมดสำหรับ" )
AWarn.Localization:AddDefinition( language_code, "cantopenconsole", 			"คุณไม่สามารถเปิดเมนูจากเซิฟเวอร์คอนโซล" )
AWarn.Localization:AddDefinition( language_code, "invalidoption", 				"การตั้งค่าผิด" )
AWarn.Localization:AddDefinition( language_code, "invalidoptionvaluetype",		"การตั้งค่าผิดประเภท" )
AWarn.Localization:AddDefinition( language_code, "optionsloaded",				"โหลดการตั้งค่าแล้ว" )
AWarn.Localization:AddDefinition( language_code, "nopunishment",				"ไม่มีบทลงโทษสำหรับจำนวนคำเตือนเท่านี้" )
AWarn.Localization:AddDefinition( language_code, "punishmentsloaded",			"โหลดการตั้งค่าบทลงโทษเสร็จแล้ว" )
AWarn.Localization:AddDefinition( language_code, "playernotallowedwarn",		"ผู้เล่นคนนี้ไม่สามารถถูกตักเตือนได้" )
AWarn.Localization:AddDefinition( language_code, "warnmessage1",				"คุณโดนตักเตือนโดย %s เพราะ %s" )
AWarn.Localization:AddDefinition( language_code, "warnmessage2",				"คุณตักเตือน %s เพราะ %s" )
AWarn.Localization:AddDefinition( language_code, "warnmessage3",				"%s ถูกตักเตือนโดย %s เพราะ %s" )
AWarn.Localization:AddDefinition( language_code, "warnmessage4",				"คุณโดนตักเตือนโดย %s" )
AWarn.Localization:AddDefinition( language_code, "warnmessage5",				"คุณตักเตือน %s" )
AWarn.Localization:AddDefinition( language_code, "warnmessage6",				"%s ถูกตักเตือนโดย %s" )
AWarn.Localization:AddDefinition( language_code, "joinmessage1",				"ได้เข้าสู่เซิฟเวอร์พร้อมกับคำตักเตือน" )
AWarn.Localization:AddDefinition( language_code, "joinmessage2",				"คำตักเตือนล่าสุด:" )
AWarn.Localization:AddDefinition( language_code, "joinmessage3",				"ยินดีต้อนรับกับเข้าสู่เซิฟเวอร์ เหมือนว่าคุณจะโดนตักเตือนมา" )
AWarn.Localization:AddDefinition( language_code, "joinmessage4",				"คุณสามารถดูคำตักเตือนของคุณได้โดยพิม" )
AWarn.Localization:AddDefinition( language_code, "closemenu",					"ปิดเมนู" )
AWarn.Localization:AddDefinition( language_code, "searchplayers",				"ค้นหาผู้เล่น" )
AWarn.Localization:AddDefinition( language_code, "viewwarnings",				"ดูคำตักเตือน" )
AWarn.Localization:AddDefinition( language_code, "configuration",				"แก้ไข" )
AWarn.Localization:AddDefinition( language_code, "clientoptions",				"ตัวเลือกผู้เล่น" )
AWarn.Localization:AddDefinition( language_code, "serveroptions",				"ตัวเลือกเซิฟเวอร์" )
AWarn.Localization:AddDefinition( language_code, "colorcustomization",			"แก้ไขสี" )
AWarn.Localization:AddDefinition( language_code, "colorselection",				"เลือกสี" )
AWarn.Localization:AddDefinition( language_code, "languageconfiguration",		"แก้ไขภาษา" )
AWarn.Localization:AddDefinition( language_code, "selectlanguage",				"เลือกภาษา" )
AWarn.Localization:AddDefinition( language_code, "enablekickpunish",			"เปิดให้ใช้การเตะเป็นบทลงโทษ" )
AWarn.Localization:AddDefinition( language_code, "enablebanpunish",				"เปิดให้ใช้การแบนเป็นบทลงโทษ" )
AWarn.Localization:AddDefinition( language_code, "enabledecay",					"เปิดให้คำเตือนที่เปิดใช้งานอยู่สามารถหายไปได้" )
AWarn.Localization:AddDefinition( language_code, "resetafterban",				"รีเซตคำเตือนที่เปิดใช้งานอยู่หลังจากโดนแบน" )
AWarn.Localization:AddDefinition( language_code, "allowwarnadmins",				"อนุญาตให้ตักเตือนแอดมิน" )
AWarn.Localization:AddDefinition( language_code, "pressenter",					"กด Enter เพื่อบันทึก" )
AWarn.Localization:AddDefinition( language_code, "entertosave",					"กด Enter เพื่อบันทึก" )
AWarn.Localization:AddDefinition( language_code, "chatprefix",					"คำนำหน้าข้อความ" )
AWarn.Localization:AddDefinition( language_code, "warningdecayrate",			"การตักเตือนจะหายไปหลังจาก (ในนาที)" )
AWarn.Localization:AddDefinition( language_code, "serverlanguage",				"ภาษาของเซิฟเวอร์" )
AWarn.Localization:AddDefinition( language_code, "punishmentsconfiguration",	"แก้ไขบทลงโทษ" )
AWarn.Localization:AddDefinition( language_code, "addpunishment",				"เพิ่มบทลงโทษ" )
AWarn.Localization:AddDefinition( language_code, "warnings",					"คำตักเตือน" )
AWarn.Localization:AddDefinition( language_code, "punishtype",					"ประเภทของบทลงโทษ" )
AWarn.Localization:AddDefinition( language_code, "punishlength",				"ระยะเวลาของบทลงโทษ" )
AWarn.Localization:AddDefinition( language_code, "playermessage",				"ข้อความของผู้เล่น" )
AWarn.Localization:AddDefinition( language_code, "playername",					"ชื่อผู้เล่น" )
AWarn.Localization:AddDefinition( language_code, "messagetoplayer",				"ส่งข้อความไปยังผู้เล่น" )
AWarn.Localization:AddDefinition( language_code, "servermessage",				"ข้อความจากเซิฟเวอร์" )
AWarn.Localization:AddDefinition( language_code, "messagetoserver",				"ส่งข้อความไปยังเซิฟเวอร์" )
AWarn.Localization:AddDefinition( language_code, "deletewarning",				"ลบคำตักเตือน" )
AWarn.Localization:AddDefinition( language_code, "punishaddmenu",				"เมนูเพิ่มบทลงโทษ" )
AWarn.Localization:AddDefinition( language_code, "inminutes",					"ในนาที" )
AWarn.Localization:AddDefinition( language_code, "0equalperma",					"0 = ถาวร" )
AWarn.Localization:AddDefinition( language_code, "use%",						"กด %s เพื่อดูชื่อผู้เล่น" )
AWarn.Localization:AddDefinition( language_code, "setdefault",					"ตั้งค่าเริ่มต้น" )
AWarn.Localization:AddDefinition( language_code, "showingownwarnings",			"ดูการตักเตือนของตนเอง" )
AWarn.Localization:AddDefinition( language_code, "warnedby",					"ถูกตักเตือนโดย" )
AWarn.Localization:AddDefinition( language_code, "warningserver",				"เซิฟเวอร์ที่ตักเตือน" )
AWarn.Localization:AddDefinition( language_code, "warningreason",				"เหตุผลที่ตักเตือน" )
AWarn.Localization:AddDefinition( language_code, "warningdate",					"วันที่ตักเตือน" )
AWarn.Localization:AddDefinition( language_code, "nothing",						"ไม่" )
AWarn.Localization:AddDefinition( language_code, "submit",						"ตกลง" )
AWarn.Localization:AddDefinition( language_code, "connectedplayers",			"ผู้เล่นที่เชื่อมต่ออยู่" )
AWarn.Localization:AddDefinition( language_code, "displaywarningsfor",			"แสดงคำตักเตือนสำหรับ" )
AWarn.Localization:AddDefinition( language_code, "activewarnings",				"คำเตือนที่ใช้งานอยู่" )
AWarn.Localization:AddDefinition( language_code, "selectedplayernowarnings",	"ผู้เล่นที่ถูกเลือกไม่เคยถูกตักเตือน" )
AWarn.Localization:AddDefinition( language_code, "selectplayerseewarnings",		"เลือกผู้เล่นที่ต้องการจะดูการตักเตือน" )
AWarn.Localization:AddDefinition( language_code, "warnplayer",					"ตักเตือนผู้เล่น" )
AWarn.Localization:AddDefinition( language_code, "reduceactiveby1",				"ลด 1 คำเตือนที่ใช้งานอยู่" )
AWarn.Localization:AddDefinition( language_code, "playerwarningmenu",			"เมนูตักเตือนผู้เล่น" )
AWarn.Localization:AddDefinition( language_code, "playersearchmenu",			"เมนูค้นหาผู้เล่น" )
AWarn.Localization:AddDefinition( language_code, "warningplayer",				"ตักเตือนผู้เล่น" )
AWarn.Localization:AddDefinition( language_code, "excludeplayers",				"ยกเว้นผู้เล่นที่ไม่เคยถูกเตือน" )
AWarn.Localization:AddDefinition( language_code, "searchforplayers",			"ค้นหาผู้เล่นจาก ชื่อ หรือ SteamID64" )
AWarn.Localization:AddDefinition( language_code, "name",						"ชื่อ" )
AWarn.Localization:AddDefinition( language_code, "lastplayed",					"เล่นครั้งล่าสุด" )
AWarn.Localization:AddDefinition( language_code, "lastwarned",					"เตือนครั้งล่าสุด" )
AWarn.Localization:AddDefinition( language_code, "never",						"ไม่เคย" )
AWarn.Localization:AddDefinition( language_code, "playerid",					"ไอดีผู้เล่น" )
AWarn.Localization:AddDefinition( language_code, "lookupplayerwarnings",		"ค้นหาคำเตือนของผู้เล่น" )
AWarn.Localization:AddDefinition( language_code, "servername",					"ชื่อเซิฟเวอร์" )
AWarn.Localization:AddDefinition( language_code, "clientjoinmessage",			"แสดงจำนวนคำเตือนให้ผู้เล่นเข้าร่วม" )
AWarn.Localization:AddDefinition( language_code, "adminjoinmessage",			"แสดงข้อความถึงผู้ดูแลระบบเมื่อผู้เล่นเข้าร่วมพร้อมคำเตือน" )
AWarn.Localization:AddDefinition( language_code, "punishmentoptions",			"การลงโทษ" )
AWarn.Localization:AddDefinition( language_code, "kickpunishdescription",		"หากเปิดใช้งาน AWarn3 สามารถเตะผู้เล่นจากเซิร์ฟเวอร์เพื่อลงโทษ" )
AWarn.Localization:AddDefinition( language_code, "banpunishdescription",		"หากเปิดใช้งาน AWarn3 สามารถแบนผู้เล่นจากเซิร์ฟเวอร์เป็นการลงโทษ" )
AWarn.Localization:AddDefinition( language_code, "enabledecaydescription",		"หากเปิดใช้งาน คำเตือนที่ทำงานอยู่จะลดลงเมื่อเวลาผ่านไป" )
AWarn.Localization:AddDefinition( language_code, "reasonrequireddescription",	"หากเปิดใช้งาน ผู้ดูแลระบบจะต้องให้เหตุผลในคำเตือน" )
AWarn.Localization:AddDefinition( language_code, "resetafterbandescription",	"หากเปิดใช้งาน คำเตือนที่แอ็คทีฟของผู้ใช้จะรีเซ็ตเป็น 0 หลังจากที่ถูกแบนโดย AWarn3" )
AWarn.Localization:AddDefinition( language_code, "logevents",					"บันทึกเหตุการณ์คำเตือน" )
AWarn.Localization:AddDefinition( language_code, "logeventsdescription",		"หากเปิดใช้งาน การดำเนินการภายใน AWarn3 จะถูกบันทึกลงในไฟล์ข้อความ" )
AWarn.Localization:AddDefinition( language_code, "allowwarnadminsdescription",	"หากเปิดใช้งาน ผู้ดูแลระบบจะสามารถเตือนผู้ดูแลระบบคนอื่นได้" )
AWarn.Localization:AddDefinition( language_code, "clientjoinmessagedescription","หากเปิดใช้งาน ผู้ใช้ที่เข้าร่วมเซิร์ฟเวอร์จะเห็นข้อความในแชทหากมีคำเตือน" )
AWarn.Localization:AddDefinition( language_code, "adminjoinmessagedescription",	"หากเปิดใช้งาน ผู้ดูแลระบบบนเซิร์ฟเวอร์จะเห็นเมื่อมีผู้เล่นเข้าร่วมซึ่งมีคำเตือน" )
AWarn.Localization:AddDefinition( language_code, "chatprefixdescription",		"คำสั่งแชทที่ใช้สำหรับคำสั่ง AWarn3 ค่าเริ่มต้น: !warn" )
AWarn.Localization:AddDefinition( language_code, "warningdecayratedescription",	"เวลา (เป็นนาที) ที่ผู้เล่นต้องเชื่อมต่อเพื่อให้มีการเตือน 1 ครั้งจึงจะสลายตัว" )
AWarn.Localization:AddDefinition( language_code, "servernamedescription",		"ชื่อของเซิร์ฟเวอร์นี้ สิ่งนี้มีประโยชน์สำหรับการตั้งค่าเซิร์ฟเวอร์หลายตัว" )
AWarn.Localization:AddDefinition( language_code, "selectlanguagedescription",	"นี่คือภาษาที่ข้อความเซิร์ฟเวอร์จะแสดง" )
AWarn.Localization:AddDefinition( language_code, "theme",						"ธีมอินเทอร์เฟซ" )
AWarn.Localization:AddDefinition( language_code, "themeselect",					"เลือกธีม" )
AWarn.Localization:AddDefinition( language_code, "punishgroup",					"กลุ่มลงโทษ" )
AWarn.Localization:AddDefinition( language_code, "grouptoset",					"กลุ่มที่จะตั้งค่า" )
AWarn.Localization:AddDefinition( language_code, "viewnotes",					"ดูบันทึกย่อของผู้เล่น" )
AWarn.Localization:AddDefinition( language_code, "playernotes",					"หมายเหตุผู้เล่น" )
AWarn.Localization:AddDefinition( language_code, "interfacecustomizations",		"การปรับแต่งอินเทอร์เฟซ" )
AWarn.Localization:AddDefinition( language_code, "enableblur",					"เปิดใช้งานพื้นหลังเบลอ" )
AWarn.Localization:AddDefinition( language_code, "chooseapreset",				"เลือกที่ตั้งไว้ล่วงหน้า (ไม่บังคับ)" )
AWarn.Localization:AddDefinition( language_code, "warningpresets",				"ที่ตั้งไว้ล่วงหน้า" )
AWarn.Localization:AddDefinition( language_code, "addeditpreset",				"เพิ่ม/แก้ไขพรีเซ็ต" )
AWarn.Localization:AddDefinition( language_code, "presetname",					"ชื่อที่ตั้งไว้ล่วงหน้า" )
AWarn.Localization:AddDefinition( language_code, "presetreason",				"เหตุผลที่ตั้งไว้ล่วงหน้า" )
