EVoice.Constants = {}

-- Configuration constants
EVoice.Constants["config"] = {

	-- Every *calculationCooldown* seconds, the addon will calculate the voice range. 
	-- Don't touch it if you don't know what you're doing.
	["calculationCooldown"] = 0.5,

	-- Radius of the circle that will be drawn around the player.
	["circleRadius"] = 15,

	-- Distance players can hear the megaphone (don't forget the ^2 at the end)
	["megaphoneRange"] = 2500 ^ 2,

	-- Sphere quality (the higher the value, the laggy it will be)
	["sphereQuality"] = 50,

	-- Max frequencies
	["maxFrequencies"] = 200,

	-- Textures exceptions for the hearing through a wall
	["texturesLOSWhitelist"] = {
		["TOOLS/TOOLSBLACK"] = true,
		["TOOLS/TOOLSSKYBOX"] = true,
		["TOOLS/TOOLSINVISIBLE"] = true,
		["METAL/METALBAR001C"] = true,
		["TOOLS/TOOLSNODRAW"] = true
	},

	-- Entities classes exceptions for the hearing through a wall
	["classesLOSWhitelist"] = {
		["prop_physics"] = true,
	},

	-- Distance to the door to be able to talk through it
	["doorTalkDistance"] = 200 ^ 2,

	-- Distance to the vehicle to be able to talk through it
	["vehicleTalkDistance"] = 200 ^ 2,

}

-- Colors constants
EVoice.Constants["colors"] = {
	["negativeStencil"] = Color(0, 0, 0, 0),
	["megaphoneCircle"] = Color(142, 68, 173),
	["frameBackground"] = Color(31, 32, 32),
	["contentBackground"] = Color(26, 27, 27),
	["lightGray"] = Color(200, 200, 200),
	["textEntryBackground"] = Color(54, 54, 54),
	["confirm"] = Color(29, 172, 89),
	["danger"] = Color(231, 76, 60)
}

-- Materials constants
EVoice.Constants["materials"] = {
	["rightClick"] = Material("evoice/right-click.png", "smooth"),
	["leftClick"] = Material("evoice/left-click.png", "smooth"),
	["frequency"] = Material("evoice/frequency.png", "smooth")
}