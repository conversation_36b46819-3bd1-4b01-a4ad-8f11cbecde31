AddCSLuaFile("cl_init.lua")
AddCSLuaFile("shared.lua")
include("shared.lua")

function ENT:Initialize()
    self:SetModel("models/foc/billy/guydragon.mdl")
    self:SetSequence("guydragonsloop")
    self:SetPlaybackRate(0.1)
    self:SetCycle(0)

    self:PhysicsInit(SOLID_VPHYSICS)
    self:SetMoveType(MOVETYPE_VPHYSICS)
    local phys = self:GetPhysicsObject()
    if (phys:IsValid()) then
        phys:Wake()
        phys:AddGameFlag(FVPHYSICS_NO_IMPACT_DMG)
    end
end

function ENT:Think()
    local seq = self:GetSequence()
    local len = self:SequenceDuration(seq) + 0.2

    if self:GetCycle() >= 1 then
        self:SetCycle(0)
    end

    self:SetPlaybackRate( -0.2 )
    self:SetCycle(self:GetCycle() + FrameTime() / len)
    self:NextThink(CurTime())
    return true
end