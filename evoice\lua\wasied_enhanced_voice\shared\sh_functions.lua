EVoice.VoiceModes = {}

-- Register a new voice mode
function EVoice:RegisterVoiceMode(sName, iMaxDistance, cColor, bPreventHearingThroughWalls, iSpecificOrder)

    if not isstring(sName) then
        return false, <PERSON>rror("[EVoice] Please specify a valid name")
    end

    if not isnumber(iMaxDistance) then
        return false, <PERSON>rror("[EVoice] Please specify a valid maximum distance")
    end

    if not IsColor(cColor) then
        return false, <PERSON>rror("[EVoice] Please specify a valid color")
    end
    
    table.insert(EVoice.VoiceModes, iSpecificOrder or (#EVoice.VoiceModes + 1), {
        sName = sName,
        cColor = cColor,
        iMaxDistanceSqr = iMaxDistance ^ 2,
        bPreventHearingThroughWalls = bPreventHearingThroughWalls
    })

end

-- Register voice modes from the config
function EVoice:LoadConfig()

    self.VoiceModes = {}

    for _, t in ipairs(EVoice.Config.Modes) do
        self:RegisterVoiceMode(t.ModeName, t.HearingDistance, t.Mode<PERSON>, t.PreventHearingThroughWalls)
    end

    if SERVER then

        for _, p in ipairs(player.GetAll()) do
            if IsValid(p) then
                p:SetVoiceMode(1, true)
            end
        end

    end

end
EVoice:LoadConfig()

-- Get the voice range by id
function EVoice:GetModeRange(iMode)
    return EVoice.VoiceModes[iMode].iMaxDistanceSqr
end

-- Get the color of the range by id
function EVoice:GetModeColor(iMode)
    return EVoice.VoiceModes[iMode].cColor
end

-- Get the name of the range by id
function EVoice:GetModeName(iMode)
    return EVoice.VoiceModes[iMode].sName
end

-- Get the through-wall range by id
function EVoice:PreventThroughWallsMode(iMode)
    return EVoice.VoiceModes[iMode].bPreventHearingThroughWalls == true
end