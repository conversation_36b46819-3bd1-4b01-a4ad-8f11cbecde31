local language_code = "TR"
 
AWarn.Localization:RegisterLanguage( language_code, "Türkçe" )

--[[Turkish translation by Mew]]--
--https://www.gmodstore.com/users/Mew
 
AWarn.Localization:AddDefinition( language_code, "welcome1",                    "AWarn3'e Hoş Geldin" )
AWarn.Localization:AddDefinition( language_code, "insufficientperms",           "Bu Komutu Gerçekleştirmek İçin Yetkiniz Bulunmuyor." )
AWarn.Localization:AddDefinition( language_code, "insufficientperms2",          "Bu Oyuncunun Uyarılarını Görmek İçin Yetkiniz Bulunmuyor." )
AWarn.Localization:AddDefinition( language_code, "commandnonexist",             "Bu Komut Geçerli Değil." )
AWarn.Localization:AddDefinition( language_code, "invalidtargetid",             "Geçersiz Hedef ya da ID." )
AWarn.Localization:AddDefinition( language_code, "invalidtarget",               "Geçersiz Hedef." )
AWarn.Localization:AddDefinition( language_code, "reasonrequired",              "Uyarı Sebebini Yaz." )
AWarn.Localization:AddDefinition( language_code, "remove1activewarn",           "1 Aktif Uyarıyı Sildin" )
AWarn.Localization:AddDefinition( language_code, "deletedwarningid",            "Uyarı Silindi ID" )
AWarn.Localization:AddDefinition( language_code, "removeallwarnings",           "Tüm Uyarılarını Sildin" )
AWarn.Localization:AddDefinition( language_code, "deletedwarningsfor",          "Tüm Uyarıları Silindi" )
AWarn.Localization:AddDefinition( language_code, "cantopenconsole",             "Konsolu Kullanarak Menüye Erişemezsin." )
AWarn.Localization:AddDefinition( language_code, "invalidoption",               "Geçersiz Ayarlama." )
AWarn.Localization:AddDefinition( language_code, "invalidoptionvaluetype",      "Invalid Option Value Type." )
AWarn.Localization:AddDefinition( language_code, "optionsloaded",               "Ayarlar Yüklendi!" )
AWarn.Localization:AddDefinition( language_code, "nopunishment",                "Uyarı İçin Yeterli Cezası Yok." )
AWarn.Localization:AddDefinition( language_code, "punishmentsloaded",           "Uyarılar Yüklendi!" )
AWarn.Localization:AddDefinition( language_code, "playernotallowedwarn",        "Bu Oyuncuyu Uyaramazsın." )
AWarn.Localization:AddDefinition( language_code, "warnmessage1",                "Yetkili %s Tarafından Belirtilen Sebeplerle Uyarıldın %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage2",                "Uyardın %s Sebep %s" )
AWarn.Localization:AddDefinition( language_code, "warnmessage3",                "%s Tarafından Uyarıldı %s Sebep %s" )
AWarn.Localization:AddDefinition( language_code, "warnmessage4",                "%s tarafından uyarıldın." )
AWarn.Localization:AddDefinition( language_code, "warnmessage5",                "Uyardın %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage6",                "%s Tarafından Uyarıldı %s." )
AWarn.Localization:AddDefinition( language_code, "joinmessage1",                "Uyarıyla Sunucuya Bağlandı." )
AWarn.Localization:AddDefinition( language_code, "joinmessage2",                "Son Uyarıları:" )
AWarn.Localization:AddDefinition( language_code, "joinmessage3",                "Sunucumuza Hoşgeldin! Önceden Uyarıların Mevcut Dikkatli Ol." )
AWarn.Localization:AddDefinition( language_code, "joinmessage4",                "Uyarılarınızı Yazarak Görebilirsin" )
AWarn.Localization:AddDefinition( language_code, "closemenu",                   "Menüyü Kapat" )
AWarn.Localization:AddDefinition( language_code, "searchplayers",               "Oyuncuları Ara" )
AWarn.Localization:AddDefinition( language_code, "viewwarnings",                "Uyarıları Gör" )
AWarn.Localization:AddDefinition( language_code, "configuration",               "Ayarlar" )
AWarn.Localization:AddDefinition( language_code, "clientoptions",               "Kullanıcı Ayarları" )
AWarn.Localization:AddDefinition( language_code, "serveroptions",               "Sunucu Ayarları" )
AWarn.Localization:AddDefinition( language_code, "colorcustomization",          "Renk Düzenleme" )
AWarn.Localization:AddDefinition( language_code, "colorselection",              "Rengi Seç" )
AWarn.Localization:AddDefinition( language_code, "languageconfiguration",       "Dil Düzenleme" )
AWarn.Localization:AddDefinition( language_code, "selectlanguage",              "Dil'i Seç" )
AWarn.Localization:AddDefinition( language_code, "enablekickpunish",            "Kick Cezasını Aktif Et" )
AWarn.Localization:AddDefinition( language_code, "enablebanpunish",             "Ban Cezasını Aktif Et" )
AWarn.Localization:AddDefinition( language_code, "enabledecay",                 "Aktif Uyarıların Silinmesini Aktif Et" )
AWarn.Localization:AddDefinition( language_code, "resetafterban",               "Kişi Banlandıktan Sonra Aktif Banları Sil" )
AWarn.Localization:AddDefinition( language_code, "allowwarnadmins",             "Yetkilileri Uyarmayı Aktif Et" )
AWarn.Localization:AddDefinition( language_code, "pressenter",                  "Enter'a Basarak Değişiklikleri Onayla" )
AWarn.Localization:AddDefinition( language_code, "entertosave",                 "Enter'a Basarak Kaydet" )
AWarn.Localization:AddDefinition( language_code, "chatprefix",                  "Geçmiş Sohbet" )
AWarn.Localization:AddDefinition( language_code, "warningdecayrate",            "Aktif Uyarı Silinme Süresi (Dakika Olarak)" )
AWarn.Localization:AddDefinition( language_code, "serverlanguage",              "Sunucu Dili" )
AWarn.Localization:AddDefinition( language_code, "punishmentsconfiguration",    "Cezalandırma Ayarları" )
AWarn.Localization:AddDefinition( language_code, "addpunishment",               "Ceza Ekle" )
AWarn.Localization:AddDefinition( language_code, "warnings",                    "Uyarılar" )
AWarn.Localization:AddDefinition( language_code, "punishtype",                  "Ceza Tipi" )
AWarn.Localization:AddDefinition( language_code, "punishlength",                "Ceza Uzunluğu" )
AWarn.Localization:AddDefinition( language_code, "playermessage",               "Oyuncunun Mesajı" )
AWarn.Localization:AddDefinition( language_code, "playername",                  "Oyuncunun Adı" )
AWarn.Localization:AddDefinition( language_code, "messagetoplayer",             "Oyuncuya Mesaj" )
AWarn.Localization:AddDefinition( language_code, "servermessage",               "Sunucu Mesajı" )
AWarn.Localization:AddDefinition( language_code, "messagetoserver",             "Sunucuya Mesaj" )
AWarn.Localization:AddDefinition( language_code, "deletewarning",               "Uyarıları Sil" )
AWarn.Localization:AddDefinition( language_code, "punishaddmenu",               "Ceza Ekleme Menüsü" )
AWarn.Localization:AddDefinition( language_code, "inminutes",                   "Dakikada" )
AWarn.Localization:AddDefinition( language_code, "0equalperma",                 "0 = Perma" )
AWarn.Localization:AddDefinition( language_code, "use%",                        "Tuşunu Kullanarak %s Oyuncuların Adını Gör" )
AWarn.Localization:AddDefinition( language_code, "setdefault",                  "Varsayılana Ayarla" )
AWarn.Localization:AddDefinition( language_code, "showingownwarnings",          "Kendi Uyarılarını Gör" )
AWarn.Localization:AddDefinition( language_code, "warnedby",                    "Uyarıldı" )
AWarn.Localization:AddDefinition( language_code, "warningserver",               "Sunucu Uyarısı" )
AWarn.Localization:AddDefinition( language_code, "warningreason",               "Uyarının Sebebi" )
AWarn.Localization:AddDefinition( language_code, "warningdate",                 "Uyarı Tarihi" )
AWarn.Localization:AddDefinition( language_code, "nothing",                     "BOŞ" )
AWarn.Localization:AddDefinition( language_code, "submit",                      "Gönder" )
AWarn.Localization:AddDefinition( language_code, "connectedplayers",            "Bağlanan Oyuncular" )
AWarn.Localization:AddDefinition( language_code, "displaywarningsfor",          "Uyarıları Görebilecekler" )
AWarn.Localization:AddDefinition( language_code, "activewarnings",              "Aktif Uyarılar" )
AWarn.Localization:AddDefinition( language_code, "selectedplayernowarnings",    "Seçilen Oyuncunun Uyarısı Bulunmamakta." )
AWarn.Localization:AddDefinition( language_code, "selectplayerseewarnings",     "Oyuncuyu Seç ve Uyarılarını Gör." )
AWarn.Localization:AddDefinition( language_code, "warnplayer",                  "Oyuncuyu Uyar" )
AWarn.Localization:AddDefinition( language_code, "reduceactiveby1",             "Aktif Uyarıyı Bir'e Düşür" )
AWarn.Localization:AddDefinition( language_code, "playerwarningmenu",           "Oyuncu Uyarı Menüsü" )
AWarn.Localization:AddDefinition( language_code, "playersearchmenu",            "Oyuncu Arama Menüsü" )
AWarn.Localization:AddDefinition( language_code, "warningplayer",               "Oyuncuyu Uyar" )
AWarn.Localization:AddDefinition( language_code, "excludeplayers",              "Ceza Almayan Oyuncuları Ayrı Tut" )
AWarn.Localization:AddDefinition( language_code, "searchforplayers",            "Oyuncuları Adlarıyla ya da SteamID'leriyle Ara" )
AWarn.Localization:AddDefinition( language_code, "name",                        "İsim" )
AWarn.Localization:AddDefinition( language_code, "lastplayed",                  "Son Oynama" )
AWarn.Localization:AddDefinition( language_code, "lastwarned",                  "Son Uyarısı" )
AWarn.Localization:AddDefinition( language_code, "never",                       "Asla" )
AWarn.Localization:AddDefinition( language_code, "playerid",                    "Oyuncu ID" )
AWarn.Localization:AddDefinition( language_code, "lookupplayerwarnings",        "Bu Oyuncunun Uyarılarına Bak" )
AWarn.Localization:AddDefinition( language_code, "servername",                  "Sunucu Adı" )
AWarn.Localization:AddDefinition( language_code, "clientjoinmessage",			"Katılma sırasında oyuncuya uyarı sayısını göster" )
AWarn.Localization:AddDefinition( language_code, "adminjoinmessage",			"Oyuncu uyarılarla katıldığında yöneticilere mesaj göster" )
AWarn.Localization:AddDefinition( language_code, "punishmentoptions",			"cezalar" )
AWarn.Localization:AddDefinition( language_code, "kickpunishdescription",		"Etkinleştirilirse, AWarn3 ceza olarak oyuncuları sunucudan atabilir." )
AWarn.Localization:AddDefinition( language_code, "banpunishdescription",		"Etkinleştirilirse, AWarn3 ceza olarak oyuncuları sunucudan yasaklayabilir." )
AWarn.Localization:AddDefinition( language_code, "enabledecaydescription",		"Etkinleştirilirse, aktif uyarılar zamanla azalır." )
AWarn.Localization:AddDefinition( language_code, "reasonrequireddescription",	"Etkinleştirilirse, yöneticilerin uyarılarında bir neden belirtmeleri gerekir." )
AWarn.Localization:AddDefinition( language_code, "resetafterbandescription",	"Etkinleştirilirse, bir kullanıcının aktif uyarıları, AWarn3 tarafından yasaklandıktan sonra 0'a sıfırlanır." )
AWarn.Localization:AddDefinition( language_code, "logevents",					"Uyarı Olaylarını Günlüğe Kaydet." )
AWarn.Localization:AddDefinition( language_code, "logeventsdescription",		"Etkinleştirilirse, AWarn3 içindeki eylemler bir metin dosyasına kaydedilecektir." )
AWarn.Localization:AddDefinition( language_code, "allowwarnadminsdescription",	"Etkinleştirilirse, yöneticiler diğer yöneticileri uyarabilir." )
AWarn.Localization:AddDefinition( language_code, "clientjoinmessagedescription","Etkinleştirilirse, sunucuya katılan kullanıcılar, uyarıları varsa sohbette bir mesaj görür." )
AWarn.Localization:AddDefinition( language_code, "adminjoinmessagedescription",	"Etkinleştirilirse, sunucudaki yöneticiler, uyarıları olan herhangi bir oyuncu katıldığında görür." )
AWarn.Localization:AddDefinition( language_code, "chatprefixdescription",		"AWarn3 komutları için kullanılan sohbet komutu. Varsayılan: !warn" )
AWarn.Localization:AddDefinition( language_code, "warningdecayratedescription",	"1 aktif uyarının azalması için bir oyuncunun bağlanması gereken süre (dakika olarak)." )
AWarn.Localization:AddDefinition( language_code, "servernamedescription",		"Bu sunucunun adı. Bu, birden çok sunucu kurulumu için kullanışlıdır." )
AWarn.Localization:AddDefinition( language_code, "selectlanguagedescription",	"Bu, sunucu mesajlarının görüntüleneceği dildir." )
AWarn.Localization:AddDefinition( language_code, "theme",						"Arayüz Teması" )
AWarn.Localization:AddDefinition( language_code, "themeselect",					"Tema seçin" )
AWarn.Localization:AddDefinition( language_code, "punishgroup",					"กลุ่มลงโทษ" )
AWarn.Localization:AddDefinition( language_code, "grouptoset",					"กลุ่มที่จะตั้งค่า" )
AWarn.Localization:AddDefinition( language_code, "viewnotes",					"Oyuncu Notlarını Görüntüle" )
AWarn.Localization:AddDefinition( language_code, "playernotes",					"Oyuncu Notları" )
AWarn.Localization:AddDefinition( language_code, "interfacecustomizations",		"Arayüz Özelleştirmeleri" )
AWarn.Localization:AddDefinition( language_code, "enableblur",					"Arka Plan Bulanıklığını Etkinleştir" )
AWarn.Localization:AddDefinition( language_code, "chooseapreset",				"Bir ön ayar seçin (İsteğe bağlı)" )
AWarn.Localization:AddDefinition( language_code, "warningpresets",				"ön ayarlar" )
AWarn.Localization:AddDefinition( language_code, "addeditpreset",				"Ön Ayar Ekle/Düzenle" )
AWarn.Localization:AddDefinition( language_code, "presetname",					"Ön Ayar Adı" )
AWarn.Localization:AddDefinition( language_code, "presetreason",				"Önceden Ayarlanmış Neden" )
