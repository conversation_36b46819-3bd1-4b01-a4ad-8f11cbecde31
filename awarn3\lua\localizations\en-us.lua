local language_code = "EN-US"

AWarn.Localization:RegisterLanguage( language_code, "American English" )



AWarn.Localization:AddDefinition( language_code, "welcome1", 					"Welcome to AWarn3!" )
AWarn.Localization:AddDefinition( language_code, "insufficientperms", 			"Insufficient permissions to run this command." )
AWarn.Localization:AddDefinition( language_code, "insufficientperms2", 			"Insufficient permissions to view this player's warnings." )
AWarn.Localization:AddDefinition( language_code, "commandnonexist", 			"This command does not exist." )
AWarn.Localization:AddDefinition( language_code, "invalidtargetid", 			"Invalid target or ID." )
AWarn.Localization:AddDefinition( language_code, "invalidtarget", 				"Invalid target." )
AWarn.Localization:AddDefinition( language_code, "reasonrequired", 				"Warning Reason is required." )
AWarn.Localization:AddDefinition( language_code, "remove1activewarn", 			"You removed 1 active warning from" )
AWarn.Localization:AddDefinition( language_code, "deletedwarningid", 			"Deleted warning ID" )
AWarn.Localization:AddDefinition( language_code, "removeallwarnings", 			"You removed all warnings from" )
AWarn.Localization:AddDefinition( language_code, "deletedwarningsfor", 			"Deleted all warnings for" )
AWarn.Localization:AddDefinition( language_code, "cantopenconsole", 			"You can't open the menu from the server console." )
AWarn.Localization:AddDefinition( language_code, "invalidoption", 				"Invalid Option." )
AWarn.Localization:AddDefinition( language_code, "invalidoptionvaluetype",		"Invalid Option Value Type." )
AWarn.Localization:AddDefinition( language_code, "optionsloaded",				"Options Loaded!" )
AWarn.Localization:AddDefinition( language_code, "nopunishment",				"No punishment for this warning count." )
AWarn.Localization:AddDefinition( language_code, "punishmentsloaded",			"Punishments Loaded!" )
AWarn.Localization:AddDefinition( language_code, "playernotallowedwarn",		"This player is not allowed to be warned." )
AWarn.Localization:AddDefinition( language_code, "warnmessage1",				"You were warned by %s for %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage2",				"You warned %s for %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage3",				"%s was warned by %s for %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage4",				"You were warned by %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage5",				"You warned %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage6",				"%s was warned by %s." )
AWarn.Localization:AddDefinition( language_code, "joinmessage1",				"has joined the server with warnings." )
AWarn.Localization:AddDefinition( language_code, "joinmessage2",				"Their last warning was on:" )
AWarn.Localization:AddDefinition( language_code, "joinmessage3",				"Welcome back to the server! It appears you have been warned in the past." )
AWarn.Localization:AddDefinition( language_code, "joinmessage4",				"You can view your warnings at any time by typing" )
AWarn.Localization:AddDefinition( language_code, "joinmessage5",				"Player is joining with active warnings: " )
AWarn.Localization:AddDefinition( language_code, "closemenu",					"Close Menu" )
AWarn.Localization:AddDefinition( language_code, "searchplayers",				"Search Players" )
AWarn.Localization:AddDefinition( language_code, "viewwarnings",				"Warnings" )
AWarn.Localization:AddDefinition( language_code, "configuration",				"Configuration" )
AWarn.Localization:AddDefinition( language_code, "clientoptions",				"User Options" )
AWarn.Localization:AddDefinition( language_code, "serveroptions",				"Server Options" )
AWarn.Localization:AddDefinition( language_code, "colorcustomization",			"Color Customization" )
AWarn.Localization:AddDefinition( language_code, "colorselection",				"Color Selection" )
AWarn.Localization:AddDefinition( language_code, "languageconfiguration",		"Language Customization" )
AWarn.Localization:AddDefinition( language_code, "selectlanguage",				"Select a Language" )
AWarn.Localization:AddDefinition( language_code, "enablekickpunish",			"Enable Kick Punishment" )
AWarn.Localization:AddDefinition( language_code, "enablebanpunish",				"Enable Ban Punishment" )
AWarn.Localization:AddDefinition( language_code, "enabledecay",					"Enable Active Warning Decay" )
AWarn.Localization:AddDefinition( language_code, "resetafterban",				"Reset Active Warnings After Ban" )
AWarn.Localization:AddDefinition( language_code, "allowwarnadmins",				"Allow Warn Admins" )
AWarn.Localization:AddDefinition( language_code, "clientjoinmessage",			"Display warning count to player on join" )
AWarn.Localization:AddDefinition( language_code, "adminjoinmessage",			"Display message to admins when player joins with warnings" )
AWarn.Localization:AddDefinition( language_code, "pressenter",					"Press Enter to Save Change" )
AWarn.Localization:AddDefinition( language_code, "entertosave",					"Enter to Save" )
AWarn.Localization:AddDefinition( language_code, "chatprefix",					"Chat Prefix" )
AWarn.Localization:AddDefinition( language_code, "warningdecayrate",			"Warning Decay Rate" )
AWarn.Localization:AddDefinition( language_code, "serverlanguage",				"Server Language" )
AWarn.Localization:AddDefinition( language_code, "punishmentsconfiguration",	"Punishment Configuration" )
AWarn.Localization:AddDefinition( language_code, "addpunishment",				"Add Punishment" )
AWarn.Localization:AddDefinition( language_code, "warnings",					"Warnings" )
AWarn.Localization:AddDefinition( language_code, "punishtype",					"Punishment Type" )
AWarn.Localization:AddDefinition( language_code, "punishlength",				"Punishment Length" )
AWarn.Localization:AddDefinition( language_code, "playermessage",				"Player Message" )
AWarn.Localization:AddDefinition( language_code, "playername",					"Player Name" )
AWarn.Localization:AddDefinition( language_code, "messagetoplayer",				"Message To Player" )
AWarn.Localization:AddDefinition( language_code, "servermessage",				"Server message" )
AWarn.Localization:AddDefinition( language_code, "messagetoserver",				"Message To Server" )
AWarn.Localization:AddDefinition( language_code, "deletewarning",				"Delete Warning" )
AWarn.Localization:AddDefinition( language_code, "punishaddmenu",				"Punishment Add Menu" )
AWarn.Localization:AddDefinition( language_code, "inminutes",					"In Minutes" )
AWarn.Localization:AddDefinition( language_code, "0equalperma",					"0 = Permanent" )
AWarn.Localization:AddDefinition( language_code, "use%",						"Use %s to show the player's name" )
AWarn.Localization:AddDefinition( language_code, "setdefault",					"Set Default" )
AWarn.Localization:AddDefinition( language_code, "showingownwarnings",			"Showing Your Own Warnings" )
AWarn.Localization:AddDefinition( language_code, "warnedby",					"Warned By" )
AWarn.Localization:AddDefinition( language_code, "warningserver",				"Warning Server" )
AWarn.Localization:AddDefinition( language_code, "warningreason",				"Warning Reason" )
AWarn.Localization:AddDefinition( language_code, "warningdate",					"Warning Date" )
AWarn.Localization:AddDefinition( language_code, "nothing",						"NOTHING" )
AWarn.Localization:AddDefinition( language_code, "submit",						"Submit" )
AWarn.Localization:AddDefinition( language_code, "connectedplayers",			"Connected Players" )
AWarn.Localization:AddDefinition( language_code, "displaywarningsfor",			"Displaying Warnings For" )
AWarn.Localization:AddDefinition( language_code, "activewarnings",				"Active Warnings" )
AWarn.Localization:AddDefinition( language_code, "selectedplayernowarnings",	"Selected player has no warnings on record." )
AWarn.Localization:AddDefinition( language_code, "selectplayerseewarnings",		"Select a player to see their warnings." )
AWarn.Localization:AddDefinition( language_code, "warnplayer",					"Warn Player" )
AWarn.Localization:AddDefinition( language_code, "reduceactiveby1",				"Reduce active warnings by 1" )
AWarn.Localization:AddDefinition( language_code, "playerwarningmenu",			"Player Warning Menu" )
AWarn.Localization:AddDefinition( language_code, "playersearchmenu",			"Player Search Menu" )
AWarn.Localization:AddDefinition( language_code, "warningplayer",				"Warning Player" )
AWarn.Localization:AddDefinition( language_code, "excludeplayers",				"Exclude players with no warning history" )
AWarn.Localization:AddDefinition( language_code, "searchforplayers",			"Search for players by name or SteamID64" )
AWarn.Localization:AddDefinition( language_code, "name",						"Name" )
AWarn.Localization:AddDefinition( language_code, "lastplayed",					"Last Played" )
AWarn.Localization:AddDefinition( language_code, "lastwarned",					"Last Warned" )
AWarn.Localization:AddDefinition( language_code, "never",						"Never" )
AWarn.Localization:AddDefinition( language_code, "playerid",					"Player ID" )
AWarn.Localization:AddDefinition( language_code, "lookupplayerwarnings",		"Lookup this player's warnings" )
AWarn.Localization:AddDefinition( language_code, "servername",					"Server Name" )
AWarn.Localization:AddDefinition( language_code, "punishmentoptions",			"Punishments" )
AWarn.Localization:AddDefinition( language_code, "kickpunishdescription",		"If enabled, AWarn3 can kick players from the server as a punishment." )
AWarn.Localization:AddDefinition( language_code, "banpunishdescription",		"If enabled, AWarn3 can ban players from the server as a punishment." )
AWarn.Localization:AddDefinition( language_code, "enabledecaydescription",		"If enabled, active warnings will decay over time." )
AWarn.Localization:AddDefinition( language_code, "reasonrequireddescription",	"If enabled, admins will be required to provide a reason in their warning." )
AWarn.Localization:AddDefinition( language_code, "resetafterbandescription",	"If enabled, a users active warnings will reset to 0 after they are banned by AWarn3." )
AWarn.Localization:AddDefinition( language_code, "logevents",					"Log Warning Events." )
AWarn.Localization:AddDefinition( language_code, "logeventsdescription",		"If enabled, actions within AWarn3 will be logged to a text file." )
AWarn.Localization:AddDefinition( language_code, "allowwarnadminsdescription",	"If enabled, admins will be able to warn other admins." )
AWarn.Localization:AddDefinition( language_code, "clientjoinmessagedescription","If enabled, users who join the server will see a message in chat if they have warnings." )
AWarn.Localization:AddDefinition( language_code, "adminjoinmessagedescription",	"If enabled, admins on the server will see when any player joins who has warnings." )
AWarn.Localization:AddDefinition( language_code, "chatprefixdescription",		"The chat command used for AWarn3 commands. Default: !warn" )
AWarn.Localization:AddDefinition( language_code, "warningdecayratedescription",	"The time (in minutes) a player needs to be connected for 1 active warning to decay." )
AWarn.Localization:AddDefinition( language_code, "servernamedescription",		"The name of this server. This is useful for multiple server setups." )
AWarn.Localization:AddDefinition( language_code, "selectlanguagedescription",	"This is the language that server messages will be displayed in." )
AWarn.Localization:AddDefinition( language_code, "theme",						"Interface Theme" )
AWarn.Localization:AddDefinition( language_code, "themeselect",					"Select Theme" )
AWarn.Localization:AddDefinition( language_code, "punishgroup",					"Punishment Group" )
AWarn.Localization:AddDefinition( language_code, "grouptoset",					"Group to Set" )
AWarn.Localization:AddDefinition( language_code, "viewnotes",					"View Player Notes" )
AWarn.Localization:AddDefinition( language_code, "playernotes",					"Player Notes" )
AWarn.Localization:AddDefinition( language_code, "interfacecustomizations",		"Interface Customizations" )
AWarn.Localization:AddDefinition( language_code, "enableblur",					"Enable Background Blur" )
AWarn.Localization:AddDefinition( language_code, "chooseapreset",				"Choose a preset (Optional)" )
AWarn.Localization:AddDefinition( language_code, "warningpresets",				"Presets" )
AWarn.Localization:AddDefinition( language_code, "addeditpreset",				"Add/Edit Preset" )
AWarn.Localization:AddDefinition( language_code, "presetname",					"Preset Name" )
AWarn.Localization:AddDefinition( language_code, "presetreason",				"Preset Reason" )
AWarn.Localization:AddDefinition( language_code, "customcommand",				"Custom Command" )
AWarn.Localization:AddDefinition( language_code, "customcommandplaceholder",				"Replacements -- %n: Player Name, %s: SteamID, %i: SteamID64" )