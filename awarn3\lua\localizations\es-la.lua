local language_code = "ES-LA"

AWarn.Localization:RegisterLanguage( language_code, "Latin Spanish" )

//Credit: https://www.gmodstore.com/users/76561198116817259

AWarn.Localization:AddDefinition( language_code, "welcome1", 					"Bienvenido a AWarn3!" )
AWarn.Localization:AddDefinition( language_code, "insufficientperms", 			"No tienes permisos para ejecutar este comando." )
AWarn.Localization:AddDefinition( language_code, "insufficientperms2", 			"No tienes permisos para ver las sanciones del jugador." )
AWarn.Localization:AddDefinition( language_code, "commandnonexist", 			"Este comando no existe." )
AWarn.Localization:AddDefinition( language_code, "invalidtargetid", 			"Objetivo no valido (ID)." )
AWarn.Localization:AddDefinition( language_code, "invalidtarget", 				"Objetivo no valido." )
AWarn.Localization:AddDefinition( language_code, "reasonrequired", 				"Razon requerida." )
AWarn.Localization:AddDefinition( language_code, "remove1activewarn", 			"Haz removido una sancion de " )
AWarn.Localization:AddDefinition( language_code, "deletedwarningid", 			"Sancion(ID) borrada" )
AWarn.Localization:AddDefinition( language_code, "removeallwarnings", 			"Haz removido todas las sanciones de" )
AWarn.Localization:AddDefinition( language_code, "deletedwarningsfor", 			"Todas las sanciones borradas por" )
AWarn.Localization:AddDefinition( language_code, "cantopenconsole", 			"No puedes abrir el menu desde la consola-servidor." )
AWarn.Localization:AddDefinition( language_code, "invalidoption", 				"Opcion no valida." )
AWarn.Localization:AddDefinition( language_code, "invalidoptionvaluetype",		"Opcion no valida (Tipo valor)." )
AWarn.Localization:AddDefinition( language_code, "optionsloaded",				"Opciones cargadas!" )
AWarn.Localization:AddDefinition( language_code, "nopunishment",				"No hay castigo para ese numero de sanciones." )
AWarn.Localization:AddDefinition( language_code, "punishmentsloaded",			"Castigos cargados!" )
AWarn.Localization:AddDefinition( language_code, "playernotallowedwarn",		"Este jugador no puede ser sancionad." )
AWarn.Localization:AddDefinition( language_code, "warnmessage1",				"Haz sido sancionado por %s. Sancion: %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage2",				"Haz sancionado a %s por %s" )
AWarn.Localization:AddDefinition( language_code, "warnmessage3",				"%s ha sidos sancionado por %s. Sancion: %s" )
AWarn.Localization:AddDefinition( language_code, "warnmessage4",				"Haz sido sancionado por %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage5",				"Haz sancionado a %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage6",				"%s ha sidos sancionado por %s." )
AWarn.Localization:AddDefinition( language_code, "joinmessage1",				"se ha unido al servidor con sanciones." )
AWarn.Localization:AddDefinition( language_code, "joinmessage2",				"Su ultima sancion fue:" )
AWarn.Localization:AddDefinition( language_code, "joinmessage3",				"Bienvenido al servidor! Parece que has sido sancionado antes." )
AWarn.Localization:AddDefinition( language_code, "joinmessage4",				"Puedes ver tus sanciones ingresando" )
AWarn.Localization:AddDefinition( language_code, "closemenu",					"Cerrar menu" )
AWarn.Localization:AddDefinition( language_code, "searchplayers",				"Buscar jugadores" )
AWarn.Localization:AddDefinition( language_code, "viewwarnings",				"Ver sanciones" )
AWarn.Localization:AddDefinition( language_code, "configuration",				"Configuracion" )
AWarn.Localization:AddDefinition( language_code, "clientoptions",				"Opciones de usuario" )
AWarn.Localization:AddDefinition( language_code, "serveroptions",				"Opciones del servidor" )
AWarn.Localization:AddDefinition( language_code, "colorcustomization",			"Personalizacion de Color" )
AWarn.Localization:AddDefinition( language_code, "colorselection",				"Seleccion de color" )
AWarn.Localization:AddDefinition( language_code, "languageconfiguration",		"Personalizacion de lenguaje" )
AWarn.Localization:AddDefinition( language_code, "selectlanguage",				"Seleccionar lenguaje" )
AWarn.Localization:AddDefinition( language_code, "enablekickpunish",			"Activar castigo con kick" )
AWarn.Localization:AddDefinition( language_code, "enablebanpunish",				"Activar castigo con ban" )
AWarn.Localization:AddDefinition( language_code, "enabledecay",					"Activar Deactivacion de sanciones" )
AWarn.Localization:AddDefinition( language_code, "resetafterban",				"Reiniciar sanciones activas luego de ban" )
AWarn.Localization:AddDefinition( language_code, "allowwarnadmins",				"Habilita sancionar staff" )
AWarn.Localization:AddDefinition( language_code, "pressenter",					"Toca enter para guardar los cambios" )
AWarn.Localization:AddDefinition( language_code, "entertosave",					"Enter para guardar" )
AWarn.Localization:AddDefinition( language_code, "chatprefix",					"Prefijo del chat" )
AWarn.Localization:AddDefinition( language_code, "warningdecayrate",			"Ratio de deactivacion de sancion (en minutos)" )
AWarn.Localization:AddDefinition( language_code, "serverlanguage",				"Lenguaje del servidor" )
AWarn.Localization:AddDefinition( language_code, "punishmentsconfiguration",	"Configuracion del castigo" )
AWarn.Localization:AddDefinition( language_code, "addpunishment",				"Agregar castigo" )
AWarn.Localization:AddDefinition( language_code, "warnings",					"Sanciones" )
AWarn.Localization:AddDefinition( language_code, "punishtype",					"Tipo de castigo" )
AWarn.Localization:AddDefinition( language_code, "punishlength",				"Duracion del castigo" )
AWarn.Localization:AddDefinition( language_code, "playermessage",				"Mensaje del jugador" )
AWarn.Localization:AddDefinition( language_code, "playername",					"Nombre del jugador" )
AWarn.Localization:AddDefinition( language_code, "messagetoplayer",				"Hablar con el jugador" )
AWarn.Localization:AddDefinition( language_code, "servermessage",				"Mensaje del servidor" )
AWarn.Localization:AddDefinition( language_code, "messagetoserver",				"Hablar al servidor" )
AWarn.Localization:AddDefinition( language_code, "deletewarning",				"Borrar sancion" )
AWarn.Localization:AddDefinition( language_code, "punishaddmenu",				"Agregar castigo MENU" )
AWarn.Localization:AddDefinition( language_code, "inminutes",					"En minutos" )
AWarn.Localization:AddDefinition( language_code, "0equalperma",					"0 = Permanente" )
AWarn.Localization:AddDefinition( language_code, "use%",						"Usa %s para mostrar el nombre del jugador" )
AWarn.Localization:AddDefinition( language_code, "setdefault",					"Establecer por defecto" )
AWarn.Localization:AddDefinition( language_code, "showingownwarnings",			"Mostrar tus propias sanciones" )
AWarn.Localization:AddDefinition( language_code, "warnedby",					"Sancionado por" )
AWarn.Localization:AddDefinition( language_code, "warningserver",				"Sancion del servidor" )
AWarn.Localization:AddDefinition( language_code, "warningreason",				"Motivo de sancion" )
AWarn.Localization:AddDefinition( language_code, "warningdate",					"Fecha" )
AWarn.Localization:AddDefinition( language_code, "nothing",						"NADA" )
AWarn.Localization:AddDefinition( language_code, "submit",						"Enviar" )
AWarn.Localization:AddDefinition( language_code, "connectedplayers",			"Jugadores conectados" )
AWarn.Localization:AddDefinition( language_code, "displaywarningsfor",			"Mostrandos sanciones de" )
AWarn.Localization:AddDefinition( language_code, "activewarnings",				"Sanciones activas" )
AWarn.Localization:AddDefinition( language_code, "selectedplayernowarnings",	"El jugador seleccionado no tiene un historial." )
AWarn.Localization:AddDefinition( language_code, "selectplayerseewarnings",		"Selecciona un jugador para ver sus sanciones." )
AWarn.Localization:AddDefinition( language_code, "warnplayer",					"Sancionar jugador" )
AWarn.Localization:AddDefinition( language_code, "reduceactiveby1",				"Reducir sanciones activas a 1" )
AWarn.Localization:AddDefinition( language_code, "playerwarningmenu",			"Menu de sanciones del jugador" )
AWarn.Localization:AddDefinition( language_code, "playersearchmenu",			"Menu de busqueda de jugadores" )
AWarn.Localization:AddDefinition( language_code, "warningplayer",				"Sancionando jugador" )
AWarn.Localization:AddDefinition( language_code, "excludeplayers",				"Excluir jugadores sin historial de sanciones" )
AWarn.Localization:AddDefinition( language_code, "searchforplayers",			"Busca jugadores por nombres o por SteamID64" )
AWarn.Localization:AddDefinition( language_code, "name",						"Nombre" )
AWarn.Localization:AddDefinition( language_code, "lastplayed",					"Ultima partida" )
AWarn.Localization:AddDefinition( language_code, "lastwarned",					"Ultima sancion" )
AWarn.Localization:AddDefinition( language_code, "never",						"Nunca" )
AWarn.Localization:AddDefinition( language_code, "playerid",					"ID Jugador" )
AWarn.Localization:AddDefinition( language_code, "lookupplayerwarnings",		"Buscar sanciones de este jugador" )
AWarn.Localization:AddDefinition( language_code, "servername",					"Nombre del servidor" )
AWarn.Localization:AddDefinition( language_code, "clientjoinmessage",			"Mostrar conteo de advertencia al jugador en unirse" )
AWarn.Localization:AddDefinition( language_code, "adminjoinmessage",			"Mostrar mensaje a los administradores cuando el jugador se une con advertencias" )
AWarn.Localization:AddDefinition( language_code, "punishmentoptions",			"Castigos" )
AWarn.Localization:AddDefinition( language_code, "kickpunishdescription",		"Si está habilitado, AWarn3 puede expulsar a los jugadores del servidor como castigo." )
AWarn.Localization:AddDefinition( language_code, "banpunishdescription",		"Si está habilitado, AWarn3 puede prohibir a los jugadores del servidor como castigo." )
AWarn.Localization:AddDefinition( language_code, "enabledecaydescription",		"Si está habilitado, las advertencias activas desaparecerán con el tiempo." )
AWarn.Localization:AddDefinition( language_code, "reasonrequireddescription",	"Si está habilitado, los administradores deberán proporcionar un motivo en su advertencia." )
AWarn.Localization:AddDefinition( language_code, "resetafterbandescription",	"Si está habilitado, las advertencias activas de un usuario se restablecerán a 0 después de que sean prohibidas por AWarn3." )
AWarn.Localization:AddDefinition( language_code, "logevents",					"Registrar eventos de advertencia." )
AWarn.Localization:AddDefinition( language_code, "logeventsdescription",		"Si está habilitado, las acciones dentro de AWarn3 se registrarán en un archivo de texto." )
AWarn.Localization:AddDefinition( language_code, "allowwarnadminsdescription",	"Si está habilitado, los administradores podrán advertir a otros administradores." )
AWarn.Localization:AddDefinition( language_code, "clientjoinmessagedescription","Si está habilitado, los usuarios que se unan al servidor verán un mensaje en el chat si tienen advertencias." )
AWarn.Localization:AddDefinition( language_code, "adminjoinmessagedescription",	"Si está habilitado, los administradores del servidor verán cuándo se une algún jugador que tenga advertencias." )
AWarn.Localization:AddDefinition( language_code, "chatprefixdescription",		"El comando de chat utilizado para los comandos de AWarn3. Predeterminado: !warn" )
AWarn.Localization:AddDefinition( language_code, "warningdecayratedescription",	"El tiempo (en minutos) que un jugador necesita estar conectado para que 1 advertencia activa decaiga." )
AWarn.Localization:AddDefinition( language_code, "servernamedescription",		"El nombre de este servidor. Esto es útil para configuraciones de múltiples servidores." )
AWarn.Localization:AddDefinition( language_code, "selectlanguagedescription",	"Este es el idioma en el que se mostrarán los mensajes del servidor." )
AWarn.Localization:AddDefinition( language_code, "theme",						"Tema de la interfaz" )
AWarn.Localization:AddDefinition( language_code, "themeselect",					"Seleccione el tema" )
AWarn.Localization:AddDefinition( language_code, "punishgroup",					"Grupo de castigo" )
AWarn.Localization:AddDefinition( language_code, "grouptoset",					"Agrupar para configurar" )
AWarn.Localization:AddDefinition( language_code, "viewnotes",					"Ver notas del jugador" )
AWarn.Localization:AddDefinition( language_code, "playernotes",					"Notas del jugador" )
AWarn.Localization:AddDefinition( language_code, "interfacecustomizations",		"Personalizaciones de interfaz" )
AWarn.Localization:AddDefinition( language_code, "enableblur",					"Habilitar desenfoque de fondo" )
AWarn.Localization:AddDefinition( language_code, "chooseapreset",				"Elija un ajuste preestablecido (opcional)" )
AWarn.Localization:AddDefinition( language_code, "warningpresets",				"Preajustes" )
AWarn.Localization:AddDefinition( language_code, "addeditpreset",				"Agregar/editar preajuste" )
AWarn.Localization:AddDefinition( language_code, "presetname",					"Nombre preestablecido" )
AWarn.Localization:AddDefinition( language_code, "presetreason",				"Razón preestablecida" )


