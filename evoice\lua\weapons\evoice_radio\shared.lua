SWEP.PrintName = "Radio"
SWEP.Author = "Wasied"
SWEP.Category = "Wasied - EVoice"
SWEP.Purpose = ""

SWEP.Slot = 1
SWEP.SlotPos = 0
SWEP.Spawnable = true

SWEP.ViewModel = Model("models/radio/c_radio.mdl")
SWEP.WorldModel = Model("models/radio/w_radio.mdl")
SWEP.ViewModelFOV = 80
SWEP.DrawAmmo = false
SWEP.UseHands = true

SWEP.Primary.ClipSize = -1
SWEP.Primary.DefaultClip = -1
SWEP.Primary.Automatic = true
SWEP.Primary.Ammo = "none"

SWEP.Secondary.ClipSize = -1
SWEP.Secondary.DefaultClip = -1
SWEP.Secondary.Automatic = true
SWEP.Secondary.Ammo = "none"

-- Radio primary attack
function SWEP:PrimaryAttack()

    if not SERVER then return false end
    if not EVoice.Config.EnableRadio then return false end

    local pOwner = self:GetOwner()
    if not IsValid(pOwner) then return end

    if (pOwner.iPrimaryUseRadioCooldown or 0) > CurTime() then return end
    pOwner.iPrimaryUseRadioCooldown = CurTime() + 0.3

    if not pOwner:GetRadioEnabled() then return end

    pOwner:SetRadioSound(not pOwner:GetRadioSound())
    pOwner:EmitSound("evoice_radiotone.wav", 50)
    return false

end

-- Radio seconday attack
function SWEP:SecondaryAttack()

    if not SERVER then return false end
    if not EVoice.Config.EnableRadio then return false end

    local pOwner = self:GetOwner()
    if not IsValid(pOwner) then return end

    if (pOwner.iSecondaryUseRadioCooldown or 0) > CurTime() then return end
    pOwner.iSecondaryUseRadioCooldown = CurTime() + 0.3

    if not pOwner:GetRadioEnabled() then return end

    pOwner:SetRadioMic(not pOwner:GetRadioMic())
    pOwner:EmitSound("evoice_radiotone.wav", 50)
    return false

end