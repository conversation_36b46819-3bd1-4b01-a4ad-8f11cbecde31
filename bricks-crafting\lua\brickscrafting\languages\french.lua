BRICKSCRAFTING.Language = {
    ["invalidPlayer"] = "Ce joueur n'est pas disponible!",
    ["configSaved"] = "Configuration sauvegarder",

    ["adminNoPermission"] = "Vous devez être administrateur pour ouvrir ce menu (par default: superadmin)!",
    ["adminResourcesAdded"] = "Ressources ajoutées avec succès au stockage de %s.",
    ["adminAddedToStorage"] = "%s a été ajouté à votre stockage par un administrateur.",
    ["adminFailedToAddRes"] = "Echec de l'ajout des ressources!",
    ["adminStorageResCleared"] = "Le stockage et les ressources de %s ont été effacés.",
    ["adminYourStorageResCleared"] = "Votre stockage et vos ressources d'artisanat ont été approuvés par un administrateur.",
    ["adminYourStorageCleared"] = "Votre stockage d'artisanat a été effacé par un administrateur.",
    ["adminStorageCleared"] = "Le stockage de %s a été effacé.",
    ["adminDataCleared"] = "Les données d'artisanat de %s ont été effacées.",
    ["adminYourDataCleared"] = "Vos données de fabrication ont été effacées par un administrateur.",
    ["adminQuestRewardsGiven"] = "%s a rendu la quête %s et à reçu sa récompense.",
    ["adminGivenQuestRewards"] = "L'admin a terminé votre quête %s et vous avez reçu la récompense de celle-ci.",
    ["adminFailedToGiveQuest"] = "Vous n'avez pas pu récupérer cette quête, peut-être l'avez vous déjà terminé!",
    ["adminQuestGiven"] = "%s vous a été confiée par %s.",
    ["adminGivenQuest"] = "La quête %s vous a été confiée par un ADMIN.",

    ["notLearntItem"] = "Vous n'avez pas encore appris à fabriquer cet objet!",
    ["notEnoughResources"] = "Vous n'avez pas assez de ressources pour fabriquer cet objet!",
    ["completedQuest"] = "Terminé %s",
    ["mining"] = "Extraction",
    ["woodCutting"] = "Découpe en cours",

    ["collectingGarbage"] = "Collecte des ordures en cours",
    ["garbagePile"] = "Tas d'ordures",

    ["foundCraftingNPC"] = "Félicitations pour avoir trouvé le PNJ de l'artisanat!",
    ["craftingNPCHighlighted"] = " Le PNJ de l'artisanat est mis en évidence! %dm",
    ["craftingNPCTurnIn"] = "Rendre la quête à un PNJ - %dm",
    ["craftingNPC"] = "PNJ de l'artisanat",
    ["craftingNPCSkillLevelTooLow"] = "Votre level de compétence est trop bas pour apprendre cela!",
    ["craftingNPCNoMoney"] = "Vous n'avez pas assez d'argent pour apprendre cela!",
    ["craftingNPCNewItemMoney"] = "+%s pour %s",
    ["craftingNPCInvalid"] = "Objet incorrect!",
    ["craftingNPCAccepted"] = "Accepté ",
    ["craftingNPCAlready"] = "Vous avez déjà cette quête!",
    ["craftingNPCLimit"] = "Limite de quête atteinte (max %d)",
    ["craftingNPCInvalidQuest"] = "Quête invalide!",
    ["craftingNPCCancelled"] = "Annulé ",
    ["craftingNPCDontHave"] = "Vous n'avez plus cette quête!",
    ["craftingNPCErrorQuest"] =  "Une erreur s'est produite lors de la quête '%s'",
    ["craftingNPCErrorCompletion"] =  "Vous n'avez pas rempli les objectifs!",
    ["craftingNPCUpgradeMoney"] =  "+Level %d %s pour %s",
    ["craftingNPCUpgrade"] =  "+Level %d %s",
    ["craftingNPCMaxLevel"] =  "Votre %s est déjà au lvl maximum!",
    ["craftingNPCNotSkill"] =  "Vous avez besoin de %d dans %s pour mettre à jour votre %s!",
    ["craftingNPCNotMoney"] =  "Vous avez besoin de %s pour mettre à jour votre %s!",

    ["lumberAxe"] = "Hache",
    ["lumberAxeInstructions"] = "Clic gauche pour couper des arbres.",
    ["lumberAxeNoResGathered"] = "Aucune ressource n'a été recueillie à partir de cet arbre!",
    ["lumberAxeCutting"] = "Découpe en cours",

    ["pickaxe"] = "Pioche",
    ["pickaxeInstructions"] = "Clic gauche pour extraire des roches.",
    ["pickaxeNoResGathered"] = "Aucune ressource n'a été recueillie à partir de ce rocher!",

    ["storage"] = "Inventaire",
    ["storageInstructions"] = "Clic droit pour ouvrir l'inventaire.",

    -- VGUI -- 
    ["vguiChangeName"] = "Changer de nom",
    ["vguiNewNameQuestion"] = "Quel devrait être le nouveau nom?",
    ["vguiSet"] = "Ensemble",
    ["vguiCancel"] = "Annuler",
    ["vguiRemove"] = "Retirer",
    ["vguiEdit"] = "Modifier",
    ["vguiDelete"] = "Effacer",
    ["vguiConfirm"] = "Confirmer",
    ["vguiDeleteConfirm"] = "Êtes-vous sûr de vouloir supprimer ceci?",
    ["vguiDeleteConfirmation"] = "Confirmez-vous ?",
    ["vguiYes"] = "Oui",
    ["vguiNo"] = "Non",
    ["vguiSelect"] = "Sélectionner",
    ["vguiNextPage"] = "Page Suivante",
    ["vguiSaveEdits"] = "Enregistrer les modifications",
    
    ["vguiConfigRarity"] = "Rareté",
    ["vguiConfigNewRarity"] = "Créer une nouvelle rareté",
    ["vguiConfigRarityName"] = "Nom de la rareté",
    ["vguiConfigAddRarity"] = "Ajouter la rareté",

    ["vguiConfigBench"] = "Ateliers",
    ["vguiConfigNewBench"] = "Ajouter un nouvel atelier",
    ["vguiConfigBenchID"] = "ID de l'atelier",
    ["vguiConfigBenchIDEntry"] = "Quel devrait être l'identifiant de l'atelier (ne doit contenir aucun espace)?",
    ["vguiConfigBenchName"] = "Nom de l'atelier",
    ["vguiConfigBenchNameEntry"] = "Quel devrait être le nom de l'atelier?",
    ["vguiConfigBenchModel"] = "Modèle de l'atelier",
    ["vguiConfigBenchModelEntry"] = "Quel devrait être le chemin du modèle de l'atelier??",
    ["vguiConfigBenchSkillName"] = "Nom de compétence de l'atelier",
    ["vguiConfigBenchSkillNameEntry"] = "Quel devrait être le nom de la compétence de l'atelier?",
    ["vguiConfigBenchMaxSkill"] = "Le level de Compétence maximum de l'atelier",
    ["vguiConfigBenchMaxSkillEntry"] = "Quelle devrait être la compétence de l'atelier maximum?",
    ["vguiConfigBenchMaxSkillNumber"] = "Le level maximale de la compétence doit être un nombre!",
    ["vguiConfigBenchAlreadyBench"] = "Il y a déjà un atelier avec cet identifiant!",
    ["vguiConfigBenchSkill"] = "Compétence:",
    ["vguiConfigBenchMaxSkillLvl"] = "Level maximum de compétence:",
    ["vguiConfigBenchDeleteTip"] = "(Tous les objets de cet atelier seront également supprimés)",
    ["vguiConfigBenchHint"] = "Vous pouvez modifier les informations de l'atelier ici, cliquez sur le modèle de l'atelier pour le changer.!",
    ["vguiConfigBenchSkillNamePopup"] = "Nom de la compétence",
    ["vguiConfigBenchMaxSkillPopup"] = "Level maximum de la compétence",
    ["vguiConfigBenchSaveChanges"] = "Sauvegarder les modifications",
    ["vguiConfigBenchEditor"] = "Éditeur d'atelier",

    ["vguiConfigItem"] = "Objets",
    ["vguiConfigItemNew"] = "Ajoute un nouvel objet",
    ["vguiConfigItemWhatBench"] = "Sur quel atelier cet article pourra-t-il être fabriquer?",
    ["vguiConfigItemSkill"] = "Compétence: ",
    ["vguiConfigItemMaxSkill"] = "Level maximal de la compétence: ",
    ["vguiConfigItemName"] = "Nom de l'objet",
    ["vguiConfigItemDescription"] = "Description de l'objet",
    ["vguiConfigItemLearnCost"] = "Le coût",
    ["vguiConfigItemReqSkill"] = "Compétences requises",
    ["vguiConfigItemLearnCostHint"] = "Combien coûte l'apprentissage de la fabrication cet objet",
    ["vguiConfigItemReqSkillHint"] = "Niveau de compétence nécessaire pour apprendre la fabrication de cet objet",
    ["vguiConfigItemModel"] = "Model de l'objet",
    ["vguiConfigItemModelQuestion"] = "Quel doit être le chemin du modèle de l'objet?",
    ["vguiConfigItemType"] = "Type de l'objet",
    ["vguiConfigItemMissingInfo"] = "Vos informations manquantes!",
    ["vguiConfigItemWhatRarity"] = "Quelle rareté cet article devrait-il avoir?",
    ["vguiConfigItemNone"] = "Aucune",
    ["vguiConfigItemWhatResources"] = "Quelles ressources sont nécessaires pour fabriquer cet objet??",
    ["vguiConfigItemFinish"] = "Terminer la création d'objet",
    ["vguiConfigItemAmount"] = " montant",
    ["vguiConfigItemCostToCraft"] = "Combien %s devrait-il coûter pour être fabriquer?",
    ["vguiConfigItemCreator"] = "Créateur d'objet",
    ["vguiConfigItemModelHint"] = "Vous pouvez modifier les informations de l'objet ici, cliquez sur le modèle de l'objet pour le modifier.!",
    ["vguiConfigItemChangeRarity"] = "Changer la rareté des objets",
    ["vguiConfigItemChangeResCost"] = "Changer le coût des ressources",
    ["vguiConfigItemTypeInfo"] = "Information sur le type de l'objet",
    ["vguiConfigItemSetRarity"] = "Définir la rareté",
    ["vguiConfigItemSetResCost"] = "Définir le coût des ressources",
    ["vguiConfigItemEditor"] = "Editeur d'objet",

    ["vguiConfigResource"] = "Resources",
    ["vguiConfigResourceAddNew"] = "Ajouter une nouvelle ressource",
    ["vguiConfigResourceNew"] = "Créer une nouvelle ressource",
    ["vguiConfigResourceName"] = "Nom de la ressource",
    ["vguiConfigResourceNameEntry"] = "Quel devrait être le nom de la ressource?",
    ["vguiConfigResourceModel"] = "Modèle de la ressource",
    ["vguiConfigResourceModelEntry"] = "Quel devrait être le chemin du modèle de ressource?",
    ["vguiConfigResourceIcon"] = "Icône de la ressource",
    ["vguiConfigResourceIconEntry"] = "Quel devrait être le chemin de l'icône de la ressource?",
    ["vguiConfigResourceNameAlready"] = "Une ressource portant ce nom existe déjà.",
    ["vguiConfigResourceChangeModel"] = "Changer de modèle",
    ["vguiConfigResourceChangeIcon"] = "Changer d'icône",
    ["vguiConfigResourceChangeColor"] = "Changer de couleur",
    ["vguiConfigColorEditor"] = "Editeur de couleur",

    ["vguiConfigGarbage"] = "Ordures",
    ["vguiConfigGarbageCollectTime"] = "Temps de récoltes",
    ["vguiConfigGarbageCollectTimeHint"] = "Combien de temps faut-il pour ramasser des ordures?",
    ["vguiConfigGarbageTotalPercent"] = "Pourcentage total: %d%%",
    ["vguiConfigGarbageResourcePercentage"] = "Pourcentage de ressources",
    ["vguiConfigGarbageChanceRes"] = "Quelle devrait être la chance d'obtenir cette ressource?",

    ["vguiConfigMiningNewRock"] = "Ajouter une nouvelle roche",
    ["vguiConfigMiningRockCreation"] = "Création d'une roche",
    ["vguiConfigMiningRockName"] = "Quel devrait être le nom de la roche?",
    ["vguiConfigMiningAlreadyRock"] = "Il y a déjà un rocher avec ce nom!",
    ["vguiConfigMiningMineTime"] = "Temps de minage: %d secondes",
    ["vguiConfigMiningRockModel"] = "Modèle de la roche",
    ["vguiConfigMiningRockModelPath"] = "Quel devrait être le chemin du modèle de la roche?",
    ["vguiConfigMiningRockModelHint"] = "Vous pouvez entrer les informations sur la roche ici, cliquez sur le modèle de roche pour le modifier.!",
    ["vguiConfigMiningRockMineTime"] = "Temps de minage",
    ["vguiConfigMiningRockMineTimeHint"] = "Combien de temps faut-il pour miner la roche",
    ["vguiConfigMiningRockCreate"] = "Créateur de roche",
    ["vguiConfigMiningRockPercentage"] = " pourcentage",
    ["vguiConfigMiningRockRes"] = "Quelle devrait être la chance d'obtenir %s?",
    ["vguiConfigMiningRockCreator"] = "Créateur de roche",
    ["vguiConfigMiningRockColor"] = "Changer la couleur de la roche",
    ["vguiConfigMiningRockResRewards"] = "Changer les récompenses des ressources",
    ["vguiConfigMiningRockSetColor"] = "Définir la couleur",
    ["vguiConfigMiningRockSetRes"] = "Définir les récompenses des ressources",
    ["vguiConfigMiningRockEditor"] = "Editeur de roche",
    ["vguiConfigMiningRockRewardAm"] =  "Montant de la récompense",
    ["vguiConfigMiningRockRewardAmHint"] =  "Combien de ressources est donnée par coup",

    ["vguiConfigWood"] = "Arbres",
    ["vguiConfigWoodNewTree"] = "Ajouter un nouvel arbre",
    ["vguiConfigWoodCreation"] = "Création d'arbres",
    ["vguiConfigWoodNameOfTree"] = "Quel devrait être le nom de l'arbre?",
    ["vguiConfigWoodAlreadyTree"] = "Il y a déjà un arbre avec ce nom!",
    ["vguiConfigWoodCuttingTime"] = "Temps de découpe: %d secondes",
    ["vguiConfigWoodTreeModel"] =  "Modèle d'arbre",
    ["vguiConfigWoodTreeModelPath"] =  "Quel doit être le chemin du modèle de l'objet?",
    ["vguiConfigWoodTreeModelHint"] =  "Vous pouvez entrer les informations de l'arbre ici, cliquez sur le modèle d'arbre pour le modifier.!",
    ["vguiConfigWoodTreeCuttingTime"] =  "Temps de découpe",
    ["vguiConfigWoodTreeCuttingTimeHint"] =  "Combien de temps faut-il pour couper l'arbre",
    ["vguiConfigWoodTreeCreate"] =  "Créer un arbre",
    ["vguiConfigWoodTreeCreator"] =  "Créateur d'arbre",
    ["vguiConfigWoodTreeColor"] =  "Changer la couleur de l'arbre",
    ["vguiConfigWoodTreeEditor"] =  "Éditeur d'arbre",
    ["vguiConfigWoodTreeRewardAm"] =  "Montant de la récompense",
    ["vguiConfigWoodTreeRewardAmHint"] =  "Combien de ressources est donnée par coup",

    ["vguiConfigQuest"] = "Quêtes",
    ["vguiConfigQuestNewQuest"] = "Ajouter une nouvelle quête",
    ["vguiConfigQuestRewardsList"] = "RÉCOMPENSES:   ",
    ["vguiConfigQuestName"] = "Nom de la quête",
    ["vguiConfigQuestDes"] = "Description de la quête",
    ["vguiConfigQuestNeedIcon"] = "Vous devez choisir une icône!",
    ["vguiConfigQuestWhatDo"] = "Qu'est-ce que le joueur doit faire?",
    ["vguiConfigQuestRewards"] = "Quelles récompenses le joueur devrait-il recevoir?",
    ["vguiConfigQuestMoney"] = "Argent",
    ["vguiConfigQuestAmount"] = "Montant",
    ["vguiConfigQuestCreate"] = "Créer une quête",
    ["vguiConfigQuestCreator"] = "Créateur de quête",
    ["vguiConfigQuestEditor"] = "Éditeur de quête",
    ["vguiConfigQuestDaily"] = "Quête Quotidienne",
    ["vguiConfigQuestEnabled"] = "Activée",
    ["vguiConfigQuestDisabled"] = "Désactivée",

    ["vguiConfigTools"] = "Outils",
    ["vguiConfigToolsPickaxeMax"] = "Level de compétence maximal de la pioche ",
    ["vguiConfigToolsPickaxeMaxLvl"] = "Quel est le level de compétence maximal pour la pioche?",
    ["vguiConfigToolsPickaxeNew"] = "Ajouter une nouvelle pioche",
    ["vguiConfigToolsPickaxeLevel"] = "Pioche - Level %d",
    ["vguiConfigToolsPickaxeReq"] = "%s  -  Level de compentéce %d",
    ["vguiConfigToolsPickaxeIncrease"] = "Augmenter: %d%%",
    ["vguiConfigToolsLumberAxeMax"] = "Niveau de compétence maximal à la hache",
    ["vguiConfigToolsLumberAxeMaxLvl"] = "Quel est le niveau de compétence maximal de la hache?",
    ["vguiConfigToolsLumberAxeNew"] = "Ajouter une nouvelle hache",
    ["vguiConfigToolsLumberAxeLevel"] = "Hache à bois - Level %d",
    ["vguiConfigToolsLumberAxeReq"] = "%s  -  Level de compétence %d",
    ["vguiConfigToolsLumberAxeIncrease"] = "Augmenter: %d%%",
    ["vguiConfigToolsSpeedIncrease"] = "Obtenir une augmentation (%)",
    ["vguiConfigToolsUpgradeCost"] = "Coût d'amélioration ",
    ["vguiConfigToolsReqSkill"] = "Compétences requises",
    ["vguiConfigToolsChangeColor"] = "Changer de couleur",
    ["vguiConfigToolsEditor"] = "Éditeur d'outils",
    ["vguiConfigToolsCreate"] = "Créer un outil",
    ["vguiConfigToolsCreator"] = "Outil créateur",

    ["vguiPlayersBOT"] = "BOT",
    ["vguiPlayersUnknown"] = "inconnu",
    ["vguiPlayersAssignResources"] = "Attribuer des ressources",
    ["vguiPlayersClearStorage"] = "Effacer le stockage",
    ["vguiPlayersWhatToClear"] = "Que voudriez-vous effacer?",
    ["vguiPlayersStorageClearer"] = "Nettoyer l'inventaire",
    ["vguiPlayersStorageAndResources"] = "Inventaire & Ressources",
    ["vguiPlayersClearStorageResQuestion"] = "Êtes-vous sûr de vouloir effacer le contenu de %s?",
    ["vguiPlayersStorage"] = "Inventaire",
    ["vguiPlayersClearStorageQuestion"] = "Êtes-vous sûr de vouloir effacer le contenu de l'inventaire de %s?",
    ["vguiPlayersNothing"] = "Rien",
    ["vguiPlayersClearData"] = "Effacer les données",
    ["vguiPlayersClearDataQuestion"] = "Êtes-vous sûr de vouloir effacer les données d'artisanat de %s?",
    ["vguiPlayersDataClearer"] = "Effaceur de données",
    ["vguiPlayersCompleteQuest"] = "Terminer la quête",
    ["vguiPlayersCompleteQuestQuestion"] = "Voulez-vous leur donner les récompenses de quête?",
    ["vguiPlayersQuestRewards"] = "Récompenses de la quête",

    ["vguiResourcesAssign"] = "Donner des ressources à %s",
    ["vguiResourcesNoUser"] = "AUCUN UTILISATEUR",
    ["vguiResourcesInvalidPly"] = "Joueur invalide ou aucune ressource sélectionnée!",

    ["vguiAdmin"] = "Admin",
    ["vguiAdminConfig"] = "Config",
    ["vguiAdminPlayers"] = "Joueurs",
    ["vguiAdminPlayersSetSkill"] = "Définir le level de compétence",
    ["vguiAdminPlayersSetSkillLvl"] = "A quel level voulez vous mettre leur niveau de compétence?",
    ["vguiAdminPlayersSkillSet"] = "La compétence de %s dans %s a été définie sur %d!",
    ["vguiAdminPlayersSkillYourSet"] = "Un administrateur a défini votre compétence dans %s sur %d!",

    ["vguiSearch"] = "Chercher",

    ["vguiBenchHint"] = "Vous pouvez en apprendre davantage au PNJ d'artisanat, CLIQUEZ-MOI!",
    ["vguiBenchNPCHighlight"] = "Le PNJ d'artisanat le plus proche a été mis en évidence sur votre HUD.!",
    ["vguiBenchName"] = "Nom: ",
    ["vguiBenchDescription"] = "\nDescription: ",
    ["vguiBenchUse"] = "Utiliser",
    ["vguiBenchDrop"] = "Laissez tomber",
    ["vguiBenchCraft"] = "Fabriquer",

    ["vguiNPCToolsPickLevel"] = "Pioche - Level",
    ["vguiNPCToolsPickDes"] = "Utilisé pour extraire des roches avec un bonus de collecte:",
    ["vguiNPCToolsPickCurLevel"] = "Level actuel:",
    ["vguiNPCToolsPickNextLevel"] = "Level suivant:",
    ["vguiNPCToolsPickUpgrade"] = "Améliorer",
    ["vguiNPCToolsPickFree"] = "Gratuit",
    ["vguiNPCToolsPickMax"] = "MAX",
    ["vguiNPCToolsPickAlreadyMax"] = "Votre pioche est déjà au niveau maximum!",
    ["vguiNPCToolsPickReqSkill"] = "Level de compétence attendu:",
    ["vguiNPCToolsAxeLevel"] = "Hache à bois - Level",
    ["vguiNPCToolsAxeDes"] = "Utilisé pour couper des arbres avec un bonus de récolte:",
    ["vguiNPCToolsAxeAlreadyMax"] = "Votre hache à bois est déjà au niveau maximum!",

    ["vguiNPCQuestsCurrent"] = "Quêtes en cours",
    ["vguiNPCQuestsAvailable"] = "Quêtes disponibles",
    ["vguiNPCQuestsCompleted"] = "Quêtes terminées",
    ["vguiNPCQuestsDaily"] = "(Quotidien)",
    ["vguiNPCQuestsAccept"] = "Accepter",
    ["vguiNPCQuestsHandIn"] = "Remettre",
    ["vguiNPCQuestsRewards"] = "RÉCOMPENSES:",
    ["vguiNPCQuestsProgress"] = "PROGRÈS:",

    ["vguiNPCTrainingAllItems"] = "Tous les plans d'objets appris",
    ["vguiNPCTrainingLearn"] = "Apprendre",

    ["vguiNPCShopSellFor"] = "Vendre %s pour %s",
    ["vguiNPCShopSetPrice"] = "Fixer le prix",
    ["vguiNPCShopResourcePrice"] = "Prix de la ressource",
    ["vguiNPCShopResourcePriceEntry"] = "Combien devrait coûter une unité de cette ressource?",
    ["vguiNPCShopExchange"] = "Vous avez échangé %s pour %s",

    ["vguiNPCHeader"] = "Artisanat",

    ["vguiStorage"] = "Inventaire",
    ["vguiStorageInfo"] = "Info",
    ["vguiStorageDrop"] = "Combien de %s voudriez-vous laisser tomber?",
    ["vguiStorageDropResource"] = "Déposer une ressource",

    ["craftingTypeSWEP"] = "SWEP",
    ["craftingTypeSWEPWC"] = "Classe d'armes",
    ["craftingTypeMoneyBag"] = "Sac d'argent",
    ["craftingTypeMinReward"] = "Récompense minimale",
    ["craftingTypeMaxReward"] = "Récompense maximale",
    ["craftingTypeReward"] = "Vous avez %s d'un sac d'argent!",
    ["craftingTypeArmor"] = "Armure",
    ["craftingTypeMaxArmor"] = "Armure Maximal",
    ["craftingTypeHealth"] = "Santé",
    ["craftingTypeMaxHealth"] = "Santé maximale",
    ["craftingTypeEntity"] = "Entité",
    ["craftingTypeEntityC"] = "Classe d'entité",
    ["craftingTypeResource"] = "Ressource",
    ["craftingTypeResourceKey"] = "Clé de ressource",
    ["craftingTypeResourceAm"] = "Montant de la ressource",
    ["craftingTypeCraftable"] = "Fabricable",
    ["craftingTypeCollectResources"] = "Recueillir des ressources",
    ["craftingTypeCraftItems"] = "Objets d'artisanat",
    ["craftingTypeProp"] = "Prop",
    ["craftingTypeVehicle"] = "Véhicule",
    ["craftingTypeVehicleScript"] = "Script de véhicule",

    ["toolName"] = "Entité Placer",
    ["toolNoPermission"] = "Vous n'êtes pas autorisé à utiliser cet outil.",
    ["toolInvalidEnt"] = "Type d'entité invalide, choisissez-en une valide dans le menu Outils.",
    ["toolEntPlaced"] = "Entité placée avec succès.",
    ["toolEntRemoved"] = "Entité supprimée avec succès.",
    ["toolEntOnlyBCS"] = "Vous ne pouvez utiliser cet outil que pour supprimer / créer une entité de BCS.",
    ["toolEntType"] = "Type d'entité",
    ["toolInfo"] = "L'outil place des spawns pour Brick's Crafting.  Clic gauche - Spawn. Clic droit - Supprimer.",
}