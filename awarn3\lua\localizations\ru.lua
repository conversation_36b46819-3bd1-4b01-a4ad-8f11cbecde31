local language_code = "RU"

AWarn.Localization:RegisterLanguage( language_code, "Russian" )

//Credit: https://www.gmodstore.com/users/zek

AWarn.Localization:AddDefinition( language_code, "welcome1", 					"Добро пожаловать в AWarn3!" )
AWarn.Localization:AddDefinition( language_code, "insufficientperms", 			"Недостаточно прав для запуска этой команды." )
AWarn.Localization:AddDefinition( language_code, "insufficientperms2", 			"Недостаточно прав для просмотра предупреждений этого игрока." )
AWarn.Localization:AddDefinition( language_code, "commandnonexist", 			"Эта команда не существует." )
AWarn.Localization:AddDefinition( language_code, "invalidtargetid", 			"Неверная цель или ID." )
AWarn.Localization:AddDefinition( language_code, "invalidtarget", 				"Неверная цель." )
AWarn.Localization:AddDefinition( language_code, "reasonrequired", 				"Нужно указать причину." )
AWarn.Localization:AddDefinition( language_code, "remove1activewarn", 			"Вы сняли 1 предупреждение с" )
AWarn.Localization:AddDefinition( language_code, "deletedwarningid", 			"Удалёно предупреждение с ID" )
AWarn.Localization:AddDefinition( language_code, "removeallwarnings", 			"Вы сняли все предупреждения с" )
AWarn.Localization:AddDefinition( language_code, "deletedwarningsfor", 			"Удалены все предупреждения с" )
AWarn.Localization:AddDefinition( language_code, "cantopenconsole", 			"Вы не можете открыть меню с консоли сервера." )
AWarn.Localization:AddDefinition( language_code, "invalidoption", 				"Неверные настройки." )
AWarn.Localization:AddDefinition( language_code, "invalidoptionvaluetype",		"Неверный тип значения параметра." )
AWarn.Localization:AddDefinition( language_code, "optionsloaded",				"Настройки загружены!" )
AWarn.Localization:AddDefinition( language_code, "nopunishment",				"Нет наказания за это предупреждение." )
AWarn.Localization:AddDefinition( language_code, "punishmentsloaded",			"Наказания загружены!" )
AWarn.Localization:AddDefinition( language_code, "playernotallowedwarn",		"Этот игрок не может быть предупрежден." )
AWarn.Localization:AddDefinition( language_code, "warnmessage1",				"Вы получили предупреждение %s за %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage2",				"Вы выдали предупреждение %s за %s" )
AWarn.Localization:AddDefinition( language_code, "warnmessage3",				"%s получил предупреждение от %s за %s" )
AWarn.Localization:AddDefinition( language_code, "warnmessage4",				"Вы получили предупреждение %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage5",				"Вы выдали предупреждение %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage6",				"%s получил предупреждение от %s." )
AWarn.Localization:AddDefinition( language_code, "joinmessage1",				"зашёл на сервер с предупреждениями." )
AWarn.Localization:AddDefinition( language_code, "joinmessage2",				"Его последнее предупреждение было:" )
AWarn.Localization:AddDefinition( language_code, "joinmessage3",				"Добро пожаловать на сервер! Похоже, вы получили предупреждение в прошлый раз." )
AWarn.Localization:AddDefinition( language_code, "joinmessage4",				"Вы можете просмотреть свои предупреждения в любое время, набрав" )
AWarn.Localization:AddDefinition( language_code, "closemenu",					"Закрыть меню" )
AWarn.Localization:AddDefinition( language_code, "searchplayers",				"Поиск игроков" )
AWarn.Localization:AddDefinition( language_code, "viewwarnings",				"Посмотреть предупреждения" )
AWarn.Localization:AddDefinition( language_code, "configuration",				"Конфигурация" )
AWarn.Localization:AddDefinition( language_code, "clientoptions",				"Параметры пользователя" )
AWarn.Localization:AddDefinition( language_code, "serveroptions",				"Параметры сервера" )
AWarn.Localization:AddDefinition( language_code, "colorcustomization",			"Настройка цвета" )
AWarn.Localization:AddDefinition( language_code, "colorselection",				"Выбор цвета" )
AWarn.Localization:AddDefinition( language_code, "languageconfiguration",		"Настройка языка" )
AWarn.Localization:AddDefinition( language_code, "selectlanguage",				"Выберите язык" )
AWarn.Localization:AddDefinition( language_code, "enablekickpunish",			"Включить Кик в виде наказания" )
AWarn.Localization:AddDefinition( language_code, "enablebanpunish",				"Включить Бан в виде наказания" )
AWarn.Localization:AddDefinition( language_code, "enabledecay",					"Включить авто-очищение активных варнов" )
AWarn.Localization:AddDefinition( language_code, "resetafterban",				"Обнуление предупреждений после бана" )
AWarn.Localization:AddDefinition( language_code, "allowwarnadmins",				"Разрешить предупреждать администраторов" )
AWarn.Localization:AddDefinition( language_code, "pressenter",					"Нажмите Enter, чтобы сохранить изменения" )
AWarn.Localization:AddDefinition( language_code, "entertosave",					"Enter чтобы сохранить" )
AWarn.Localization:AddDefinition( language_code, "chatprefix",					"Чат префикс" )
AWarn.Localization:AddDefinition( language_code, "warningdecayrate",			"Время авто-очищения (мин)" )
AWarn.Localization:AddDefinition( language_code, "serverlanguage",				"Язык сервера" )
AWarn.Localization:AddDefinition( language_code, "punishmentsconfiguration",	"Конфигурация наказаний" )
AWarn.Localization:AddDefinition( language_code, "addpunishment",				"Добавить наказание" )
AWarn.Localization:AddDefinition( language_code, "warnings",					"Предупреждения" )
AWarn.Localization:AddDefinition( language_code, "punishtype",					"Тип наказания" )
AWarn.Localization:AddDefinition( language_code, "punishlength",				"Длина наказания" )
AWarn.Localization:AddDefinition( language_code, "playermessage",				"Сообщение игрока" )
AWarn.Localization:AddDefinition( language_code, "playername",					"Имя игрока" )
AWarn.Localization:AddDefinition( language_code, "messagetoplayer",				"Сообщение игроку" )
AWarn.Localization:AddDefinition( language_code, "servermessage",				"Сообщение сервера" )
AWarn.Localization:AddDefinition( language_code, "messagetoserver",				"Сообщение на сервер" )
AWarn.Localization:AddDefinition( language_code, "deletewarning",				"Удалить предупреждения" )
AWarn.Localization:AddDefinition( language_code, "punishaddmenu",				"Добавить наказание - Меню" )
AWarn.Localization:AddDefinition( language_code, "inminutes",					"В минутах" )
AWarn.Localization:AddDefinition( language_code, "0equalperma",					"0 = Навсегда" )
AWarn.Localization:AddDefinition( language_code, "use%",						"Используйте %s для просмотра имени игрока" )
AWarn.Localization:AddDefinition( language_code, "setdefault",					"По умолчанию" )
AWarn.Localization:AddDefinition( language_code, "showingownwarnings",			"Показаны ваши собственные предупреждения" )
AWarn.Localization:AddDefinition( language_code, "warnedby",					"Выдал предупреждение" )
AWarn.Localization:AddDefinition( language_code, "warningserver",				"Предупреждения сервера" )
AWarn.Localization:AddDefinition( language_code, "warningreason",				"Причина предупреждения" )
AWarn.Localization:AddDefinition( language_code, "warningdate",					"Дата предупреждения" )
AWarn.Localization:AddDefinition( language_code, "nothing",						"НИЧЕГО ТАКОГО" )
AWarn.Localization:AddDefinition( language_code, "submit",						"Отправить" )
AWarn.Localization:AddDefinition( language_code, "connectedplayers",			"Подключенные игроки" )
AWarn.Localization:AddDefinition( language_code, "displaywarningsfor",			"Отображение предупреждений для" )
AWarn.Localization:AddDefinition( language_code, "activewarnings",				"Активные предупреждения" )
AWarn.Localization:AddDefinition( language_code, "selectedplayernowarnings",	"Выбранный игрок не имеет предупреждений" )
AWarn.Localization:AddDefinition( language_code, "selectplayerseewarnings",		"Выберите игрока, чтобы увидеть его предупреждения." )
AWarn.Localization:AddDefinition( language_code, "warnplayer",					"Предупредить игрока" )
AWarn.Localization:AddDefinition( language_code, "reduceactiveby1",				"Уменьшить активные предупреждения на 1" )
AWarn.Localization:AddDefinition( language_code, "playerwarningmenu",			"Меню предупреждения игрока" )
AWarn.Localization:AddDefinition( language_code, "playersearchmenu",			"Меню поиска игрока" )
AWarn.Localization:AddDefinition( language_code, "warningplayer",				"Предупреждение игрока" )
AWarn.Localization:AddDefinition( language_code, "excludeplayers",				"Исключить игроков без предупреждений" )
AWarn.Localization:AddDefinition( language_code, "searchforplayers",			"Искать игрока по нику или SteamID64" )
AWarn.Localization:AddDefinition( language_code, "name",						"Ник" )
AWarn.Localization:AddDefinition( language_code, "lastplayed",					"Последние игравшие" )
AWarn.Localization:AddDefinition( language_code, "lastwarned",					"Последние предупреждённые" )
AWarn.Localization:AddDefinition( language_code, "never",						"Никогда" )
AWarn.Localization:AddDefinition( language_code, "playerid",					"Игрок ID" )
AWarn.Localization:AddDefinition( language_code, "lookupplayerwarnings",		"Найти предупреждения этого игрока" )
AWarn.Localization:AddDefinition( language_code, "servername",					"Название сервера" )
AWarn.Localization:AddDefinition( language_code, "clientjoinmessage",			"Показывать количество предупреждений игроку при присоединении" )
AWarn.Localization:AddDefinition( language_code, "adminjoinmessage",			"Отображать сообщение администраторам, когда игрок присоединяется с предупреждениями" )
AWarn.Localization:AddDefinition( language_code, "punishmentoptions",			"Наказания" )
AWarn.Localization:AddDefinition( language_code, "kickpunishdescription",		"Если включено, AWarn3 может выгнать игроков с сервера в качестве наказания." )
AWarn.Localization:AddDefinition( language_code, "banpunishdescription",		"Если включено, AWarn3 может забанить игроков на сервере в качестве наказания." )
AWarn.Localization:AddDefinition( language_code, "enabledecaydescription",		"Если этот параметр включен, активные предупреждения со временем исчезнут." )
AWarn.Localization:AddDefinition( language_code, "reasonrequireddescription",	"Если этот параметр включен, администраторы должны будут указать причину предупреждения." )
AWarn.Localization:AddDefinition( language_code, "resetafterbandescription",	"Если этот параметр включен, активные предупреждения пользователей будут сброшены на 0 после того, как они будут заблокированы AWarn3." )
AWarn.Localization:AddDefinition( language_code, "logevents",					"Журнал событий предупреждений." )
AWarn.Localization:AddDefinition( language_code, "logeventsdescription",		"Если этот параметр включен, действия в AWarn3 будут записываться в текстовый файл." )
AWarn.Localization:AddDefinition( language_code, "allowwarnadminsdescription",	"Если этот параметр включен, администраторы смогут предупреждать других администраторов." )
AWarn.Localization:AddDefinition( language_code, "clientjoinmessagedescription","Если этот параметр включен, пользователи, которые присоединяются к серверу, будут видеть сообщение в чате, если у них есть предупреждения." )
AWarn.Localization:AddDefinition( language_code, "adminjoinmessagedescription",	"Если этот параметр включен, администраторы на сервере будут видеть, когда присоединяется любой игрок, у которого есть предупреждения." )
AWarn.Localization:AddDefinition( language_code, "chatprefixdescription",		"Команда чата, используемая для команд AWarn3. По умолчанию: !warn" )
AWarn.Localization:AddDefinition( language_code, "warningdecayratedescription",	"Время (в минутах), в течение которого игрок должен быть подключен, чтобы 1 активное предупреждение исчезло." )
AWarn.Localization:AddDefinition( language_code, "servernamedescription",		"Имя этого сервера. Это полезно при настройке нескольких серверов." )
AWarn.Localization:AddDefinition( language_code, "selectlanguagedescription",	"Это язык, на котором будут отображаться сообщения сервера." )
AWarn.Localization:AddDefinition( language_code, "theme",						"Тема интерфейса" )
AWarn.Localization:AddDefinition( language_code, "themeselect",					"Выберите тему" )
AWarn.Localization:AddDefinition( language_code, "punishgroup",					"Группа наказания" )
AWarn.Localization:AddDefinition( language_code, "grouptoset",					"Группа для установки" )
AWarn.Localization:AddDefinition( language_code, "viewnotes",					"Просмотр заметок игрока" )
AWarn.Localization:AddDefinition( language_code, "playernotes",					"Заметки игрока" )
AWarn.Localization:AddDefinition( language_code, "interfacecustomizations",		"Настройки интерфейса" )
AWarn.Localization:AddDefinition( language_code, "enableblur",					"Включить размытие фона" )
AWarn.Localization:AddDefinition( language_code, "chooseapreset",				"Выберите предустановку (необязательно)" )
AWarn.Localization:AddDefinition( language_code, "warningpresets",				"Пресеты" )
AWarn.Localization:AddDefinition( language_code, "addeditpreset",				"Добавить / изменить предустановку" )
AWarn.Localization:AddDefinition( language_code, "presetname",					"Имя предустановки" )
AWarn.Localization:AddDefinition( language_code, "presetreason",				"Предустановленная причина" )
