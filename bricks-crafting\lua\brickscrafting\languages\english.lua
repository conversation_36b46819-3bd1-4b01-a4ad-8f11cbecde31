BRICKSCRAFTING.Language = {
    ["invalidPlayer"] = "This player is invalid!",
    ["configSaved"] = "Config saved",

    ["adminNoPermission"] = "You must be an admin to open this menu (default: superadmin)!",
    ["adminResourcesAdded"] = "Resources succesfully added to %s's storage.",
    ["adminAddedToStorage"] = "%s has been added to your storage by an ADMIN.",
    ["adminFailedToAddRes"] = "Failed to add resources!",
    ["adminStorageResCleared"] = "%s's Storage & Resources were cleared.",
    ["adminYourStorageResCleared"] = "Your Crafting Storage & Resources were cleared by an ADMIN.",
    ["adminYourStorageCleared"] = "Your Crafting Storage was cleared by an ADMIN.",
    ["adminStorageCleared"] = "%s's Storage was cleared.",
    ["adminDataCleared"] = "%s's Crafting Data was cleared.",
    ["adminYourDataCleared"] = "Your Crafting Data was cleared by an ADMIN.",
    ["adminQuestRewardsGiven"] = "%s's was given the quest %s and its rewards.",
    ["adminGivenQuestRewards"] = "You have been given the quest %s and its rewards by an ADMIN.",
    ["adminFailedToGiveQuest"] = "Failed to give quest, may already be completed!",
    ["adminQuestGiven"] = "%s's was given the quest %s.",
    ["adminGivenQuest"] = "You have been given the quest %s by an ADMIN.",

    ["notLearntItem"] = "You have not learnt how to craft this item yet!",
    ["notEnoughResources"] = "You don't have enough resources to craft this item!",
    ["completedQuest"] = "Completed %s",
    ["mining"] = "Mining",
    ["woodCutting"] = "Wood Cutting",

    ["collectingGarbage"] = "Collecting Garbage",
    ["garbagePile"] = "Garbage Pile",

    ["foundCraftingNPC"] = "Congrats on finding the Crafting NPC!",
    ["craftingNPCHighlighted"] = "Crafting NPC highlighted! %dm",
    ["craftingNPCTurnIn"] = "Turn in quest at NPC - %dm",
    ["craftingNPC"] = "Crafting NPC",
    ["craftingNPCSkillLevelTooLow"] = "Your skill level is too low to learn this!",
    ["craftingNPCNoMoney"] = "You don't have enough money to learn this!",
    ["craftingNPCNewItemMoney"] = "+%s for %s",
    ["craftingNPCInvalid"] = "Invalid item!",
    ["craftingNPCAccepted"] = "Accepted ",
    ["craftingNPCAlready"] = "You already have this quest!",
    ["craftingNPCLimit"] = "Quest limit reached (max %d)",
    ["craftingNPCInvalidQuest"] = "Invalid quest!",
    ["craftingNPCCancelled"] = "Cancelled ",
    ["craftingNPCDontHave"] = "You dont have this quest anymore!",
    ["craftingNPCErrorQuest"] =  "There was an error completing the quest '%s'",
    ["craftingNPCErrorCompletion"] =  "You haven't met the completion requirements!",
    ["craftingNPCUpgradeMoney"] =  "+Level %d %s for %s",
    ["craftingNPCUpgrade"] =  "+Level %d %s",
    ["craftingNPCMaxLevel"] =  "Your %s is already max level!",
    ["craftingNPCNotSkill"] =  "You need %d in %s to upgrade your %s!",
    ["craftingNPCNotMoney"] =  "You need %s to upgrade your %s!",

    ["lumberAxe"] = "Lumber Axe",
    ["lumberAxeInstructions"] = "Left click to chop trees.",
    ["lumberAxeNoResGathered"] = "No resources were gathered from this tree!",
    ["lumberAxeCutting"] = "Cutting",

    ["pickaxe"] = "Pickaxe",
    ["pickaxeInstructions"] = "Left click to mine rocks.",
    ["pickaxeNoResGathered"] = "No resources were gathered from this rock!",

    ["storage"] = "Crafting Storage",
    ["storageInstructions"] = "Right click to open storage.",

    -- VGUI -- 
    ["vguiChangeName"] = "Change Name",
    ["vguiNewNameQuestion"] = "What should the new name be?",
    ["vguiSet"] = "Set",
    ["vguiCancel"] = "Cancel",
    ["vguiRemove"] = "Remove",
    ["vguiEdit"] = "Edit",
    ["vguiDelete"] = "Delete",
    ["vguiConfirm"] = "Confirm",
    ["vguiDeleteConfirm"] = "Are you sure you want to delete this?",
    ["vguiDeleteConfirmation"] = "Delete Confirmation",
    ["vguiYes"] = "Yes",
    ["vguiNo"] = "No",
    ["vguiSelect"] = "Select",
    ["vguiNextPage"] = "Next page",
    ["vguiSaveEdits"] = "Save Edits",
    
    ["vguiConfigRarity"] = "Rarity",
    ["vguiConfigNewRarity"] = "Create new rarity",
    ["vguiConfigRarityName"] = "Rarity name",
    ["vguiConfigAddRarity"] = "Add rarity",

    ["vguiConfigBench"] = "Benches",
    ["vguiConfigNewBench"] = "Add new bench",
    ["vguiConfigBenchID"] = "Bench ID",
    ["vguiConfigBenchIDEntry"] = "What should the bench ID be (must contain no spaces)?",
    ["vguiConfigBenchName"] = "Bench Name",
    ["vguiConfigBenchNameEntry"] = "What should the bench name be?",
    ["vguiConfigBenchModel"] = "Bench Model",
    ["vguiConfigBenchModelEntry"] = "What should the bench model path be?",
    ["vguiConfigBenchSkillName"] = "Bench Skill Name",
    ["vguiConfigBenchSkillNameEntry"] = "What should the bench skill name be?",
    ["vguiConfigBenchMaxSkill"] = "Bench Max Skill",
    ["vguiConfigBenchMaxSkillEntry"] = "What should the bench max skill be?",
    ["vguiConfigBenchMaxSkillNumber"] = "The max skill must be a number!",
    ["vguiConfigBenchAlreadyBench"] = "There is already a bench with this ID!",
    ["vguiConfigBenchSkill"] = "Skill:",
    ["vguiConfigBenchMaxSkillLvl"] = "Max skill level:",
    ["vguiConfigBenchDeleteTip"] = "(All items on this bench will also be removed)",
    ["vguiConfigBenchHint"] = "You can edit the bench info here, click the bench model to change it!",
    ["vguiConfigBenchSkillNamePopup"] = "Skill name",
    ["vguiConfigBenchMaxSkillPopup"] = "Max skill level",
    ["vguiConfigBenchSaveChanges"] = "Save changes",
    ["vguiConfigBenchEditor"] = "Bench editor",

    ["vguiConfigItem"] = "Items",
    ["vguiConfigItemNew"] = "Add new item",
    ["vguiConfigItemWhatBench"] = "What bench should this item be on?",
    ["vguiConfigItemSkill"] = "Skill: ",
    ["vguiConfigItemMaxSkill"] = "Max skill level: ",
    ["vguiConfigItemName"] = "Item Name",
    ["vguiConfigItemDescription"] = "Item Description",
    ["vguiConfigItemLearnCost"] = "Learn cost",
    ["vguiConfigItemReqSkill"] = "Required skill",
    ["vguiConfigItemLearnCostHint"] = "How much it costs to learn this item",
    ["vguiConfigItemReqSkillHint"] = "Skill level needed to learn this item",
    ["vguiConfigItemModel"] = "Item model",
    ["vguiConfigItemModelQuestion"] = "What should the item model path be?",
    ["vguiConfigItemType"] = "Item type",
    ["vguiConfigItemMissingInfo"] = "Your missing information!",
    ["vguiConfigItemWhatRarity"] = "What rarity should this item be?",
    ["vguiConfigItemNone"] = "None",
    ["vguiConfigItemWhatResources"] = "What resources are needed to craft this item?",
    ["vguiConfigItemFinish"] = "Finish item creation",
    ["vguiConfigItemAmount"] = " amount",
    ["vguiConfigItemCostToCraft"] = "How much %s should it cost to craft?",
    ["vguiConfigItemCreator"] = "Item creator",
    ["vguiConfigItemModelHint"] = "You can edit the item info here, click the item model to change it!",
    ["vguiConfigItemChangeRarity"] = "Change item rarity",
    ["vguiConfigItemChangeResCost"] = "Change resource cost",
    ["vguiConfigItemTypeInfo"] = "Item type info",
    ["vguiConfigItemSetRarity"] = "Set rarity",
    ["vguiConfigItemSetResCost"] = "Set resource cost",
    ["vguiConfigItemEditor"] = "Item editor",

    ["vguiConfigResource"] = "Resources",
    ["vguiConfigResourceAddNew"] = "Add new resource",
    ["vguiConfigResourceNew"] = "Create new resource",
    ["vguiConfigResourceName"] = "Resource Name",
    ["vguiConfigResourceNameEntry"] = "What should the resource name be?",
    ["vguiConfigResourceModel"] = "Resource Model",
    ["vguiConfigResourceModelEntry"] = "What should the resource model path be?",
    ["vguiConfigResourceIcon"] = "Resource Icon",
    ["vguiConfigResourceIconEntry"] = "What should the resource icon path be?",
    ["vguiConfigResourceNameAlready"] = "A resource with this name already exists.",
    ["vguiConfigResourceChangeModel"] = "Change model",
    ["vguiConfigResourceChangeIcon"] = "Change icon",
    ["vguiConfigResourceChangeColor"] = "Change color",
    ["vguiConfigColorEditor"] = "Color editor",

    ["vguiConfigGarbage"] = "Garbage",
    ["vguiConfigGarbageCollectTime"] = "Collect time",
    ["vguiConfigGarbageCollectTimeHint"] = "How long does it take to collect garbage?",
    ["vguiConfigGarbageTotalPercent"] = "Total percent: %d%%",
    ["vguiConfigGarbageResourcePercentage"] = "Resource Percentage",
    ["vguiConfigGarbageChanceRes"] = "What should the chance of getting this resource be?",

    ["vguiConfigMiningNewRock"] = "Add new rock",
    ["vguiConfigMiningRockCreation"] = "Rock creation",
    ["vguiConfigMiningRockName"] = "What should the name of the rock be?",
    ["vguiConfigMiningAlreadyRock"] = "There is already a rock with this name!",
    ["vguiConfigMiningMineTime"] = "Mine time: %d seconds",
    ["vguiConfigMiningRockModel"] = "Rock Model",
    ["vguiConfigMiningRockModelPath"] = "What should the rock model path be?",
    ["vguiConfigMiningRockModelHint"] = "You can enter the rock info here, click the rock model to change it!",
    ["vguiConfigMiningRockMineTime"] = "Mine time",
    ["vguiConfigMiningRockMineTimeHint"] = "How long it takes to mine the rock",
    ["vguiConfigMiningRockCreate"] = "Create rock",
    ["vguiConfigMiningRockPercentage"] = " percentage",
    ["vguiConfigMiningRockRes"] = "What should the chance to get %s be?",
    ["vguiConfigMiningRockCreator"] = "Rock creator",
    ["vguiConfigMiningRockColor"] = "Change rock color",
    ["vguiConfigMiningRockResRewards"] = "Change resource rewards",
    ["vguiConfigMiningRockSetColor"] = "Set color",
    ["vguiConfigMiningRockSetRes"] = "Set resource rewards",
    ["vguiConfigMiningRockEditor"] = "Rock editor",
    ["vguiConfigMiningRockRewardAm"] =  "Reward amount",
    ["vguiConfigMiningRockRewardAmHint"] =  "How much of a resource is given per hit",

    ["vguiConfigWood"] = "Trees",
    ["vguiConfigWoodNewTree"] = "Add new tree",
    ["vguiConfigWoodCreation"] = "Tree creation",
    ["vguiConfigWoodNameOfTree"] = "What should the name of the tree be?",
    ["vguiConfigWoodAlreadyTree"] = "There is already a tree with this name!",
    ["vguiConfigWoodCuttingTime"] = "Cutting time: %d seconds",
    ["vguiConfigWoodTreeModel"] =  "Tree Model",
    ["vguiConfigWoodTreeModelPath"] =  "What should the item model path be?",
    ["vguiConfigWoodTreeModelHint"] =  "You can enter the tree info here, click the tree model to change it!",
    ["vguiConfigWoodTreeCuttingTime"] =  "Cutting time",
    ["vguiConfigWoodTreeCuttingTimeHint"] =  "How long it takes to cut the tree",
    ["vguiConfigWoodTreeCreate"] =  "Create tree",
    ["vguiConfigWoodTreeCreator"] =  "Tree creator",
    ["vguiConfigWoodTreeColor"] =  "Change tree color",
    ["vguiConfigWoodTreeEditor"] =  "Tree editor",
    ["vguiConfigWoodTreeRewardAm"] =  "Reward amount",
    ["vguiConfigWoodTreeRewardAmHint"] =  "How much of a resource is given per hit",

    ["vguiConfigQuest"] = "Quests",
    ["vguiConfigQuestNewQuest"] = "Add new quest",
    ["vguiConfigQuestRewardsList"] = "REWARDS:   ",
    ["vguiConfigQuestName"] = "Quest name",
    ["vguiConfigQuestDes"] = "Quest description",
    ["vguiConfigQuestNeedIcon"] = "You need to choose an icon!",
    ["vguiConfigQuestWhatDo"] = "What does the player need to do?",
    ["vguiConfigQuestRewards"] = "What rewards should the player get?",
    ["vguiConfigQuestMoney"] = "Money",
    ["vguiConfigQuestAmount"] = "amount",
    ["vguiConfigQuestCreate"] = "Create quest",
    ["vguiConfigQuestCreator"] = "Quest creator",
    ["vguiConfigQuestEditor"] = "Quest editor",
    ["vguiConfigQuestDaily"] = "Daily quest",
    ["vguiConfigQuestEnabled"] = "Enabled",
    ["vguiConfigQuestDisabled"] = "Disabled",

    ["vguiConfigTools"] = "Tools",
    ["vguiConfigToolsPickaxeMax"] = "Max pickaxe skill level",
    ["vguiConfigToolsPickaxeMaxLvl"] = "What is the max skill level for the pickaxe?",
    ["vguiConfigToolsPickaxeNew"] = "Add new pickaxe",
    ["vguiConfigToolsPickaxeLevel"] = "Pickaxe - Level %d",
    ["vguiConfigToolsPickaxeReq"] = "%s  -  Skill level %d",
    ["vguiConfigToolsPickaxeIncrease"] = "Increase: %d%%",
    ["vguiConfigToolsLumberAxeMax"] = "Max lumber axe skill level",
    ["vguiConfigToolsLumberAxeMaxLvl"] = "What is the max skill level for the lumber axe?",
    ["vguiConfigToolsLumberAxeNew"] = "Add new lumber axe",
    ["vguiConfigToolsLumberAxeLevel"] = "Lumber Axe - Level %d",
    ["vguiConfigToolsLumberAxeReq"] = "%s  -  Skill level %d",
    ["vguiConfigToolsLumberAxeIncrease"] = "Increase: %d%%",
    ["vguiConfigToolsSpeedIncrease"] = "Gather increase (%)",
    ["vguiConfigToolsUpgradeCost"] = "Upgrade cost",
    ["vguiConfigToolsReqSkill"] = "Required skill",
    ["vguiConfigToolsChangeColor"] = "Change color",
    ["vguiConfigToolsEditor"] = "Tool editor",
    ["vguiConfigToolsCreate"] = "Create Tool",
    ["vguiConfigToolsCreator"] = "Tool creator",

    ["vguiPlayersBOT"] = "BOT",
    ["vguiPlayersUnknown"] = "unknown",
    ["vguiPlayersAssignResources"] = "Assign resources",
    ["vguiPlayersClearStorage"] = "Clear storage",
    ["vguiPlayersWhatToClear"] = "What would you like to clear?",
    ["vguiPlayersStorageClearer"] = "Storage clearer",
    ["vguiPlayersStorageAndResources"] = "Storage & Resources",
    ["vguiPlayersClearStorageResQuestion"] = "Are you sure you want to clear %s's Storage & Resources?",
    ["vguiPlayersStorage"] = "Storage",
    ["vguiPlayersClearStorageQuestion"] = "Are you sure you want to clear %s's Storage?",
    ["vguiPlayersNothing"] = "Nothing",
    ["vguiPlayersClearData"] = "Clear data",
    ["vguiPlayersClearDataQuestion"] = "Are you sure you want to clear %s's Crafting Data?",
    ["vguiPlayersDataClearer"] = "Data clearer",
    ["vguiPlayersCompleteQuest"] = "Complete quest",
    ["vguiPlayersCompleteQuestQuestion"] = "Would you like to give them the quest rewards?",
    ["vguiPlayersQuestRewards"] = "Quest Rewards",

    ["vguiResourcesAssign"] = "Assign resources to %s",
    ["vguiResourcesNoUser"] = "NO USER",
    ["vguiResourcesInvalidPly"] = "Invalid player or no resources selected!",

    ["vguiAdmin"] = "Admin",
    ["vguiAdminConfig"] = "Config",
    ["vguiAdminPlayers"] = "Players",
    ["vguiAdminPlayersSetSkill"] = "Set skill level",
    ["vguiAdminPlayersSetSkillLvl"] = "What would you like to set their skill to?",
    ["vguiAdminPlayersSkillSet"] = "%s's skill in %s was set to %d!",
    ["vguiAdminPlayersSkillYourSet"] = "An admin has set your skill in %s to %d!",

    ["vguiSearch"] = "Search",

    ["vguiBenchHint"] = "You can learn more items at the Crafting NPC, CLICK ME!",
    ["vguiBenchNPCHighlight"] = "The closest Crafting NPC has been highlighted on your HUD!",
    ["vguiBenchName"] = "Name: ",
    ["vguiBenchDescription"] = "\nDescription: ",
    ["vguiBenchUse"] = "Use",
    ["vguiBenchDrop"] = "Drop",
    ["vguiBenchCraft"] = "Craft",

    ["vguiNPCToolsPickLevel"] = "Pickaxe - Level",
    ["vguiNPCToolsPickDes"] = "Used to mine rocks with a gather bonus:",
    ["vguiNPCToolsPickCurLevel"] = "Current level:",
    ["vguiNPCToolsPickNextLevel"] = "Next level:",
    ["vguiNPCToolsPickUpgrade"] = "Upgrade",
    ["vguiNPCToolsPickFree"] = "FREE",
    ["vguiNPCToolsPickMax"] = "MAX",
    ["vguiNPCToolsPickAlreadyMax"] = "Your pickaxe is already max level!",
    ["vguiNPCToolsPickReqSkill"] = "Req. Skill level:",
    ["vguiNPCToolsAxeLevel"] = "Lumber Axe - Level",
    ["vguiNPCToolsAxeDes"] = "Used to cutt trees with a gather bonus:",
    ["vguiNPCToolsAxeAlreadyMax"] = "Your lumber axe is already max level!",

    ["vguiNPCQuestsCurrent"] = "Current Quests",
    ["vguiNPCQuestsAvailable"] = "Available Quests",
    ["vguiNPCQuestsCompleted"] = "Completed Quests",
    ["vguiNPCQuestsDaily"] = "(Daily)",
    ["vguiNPCQuestsAccept"] = "Accept",
    ["vguiNPCQuestsHandIn"] = "Hand in",
    ["vguiNPCQuestsRewards"] = "REWARDS:",
    ["vguiNPCQuestsProgress"] = "PROGRESS:",

    ["vguiNPCTrainingAllItems"] = "All items learned",
    ["vguiNPCTrainingLearn"] = "Learn",

    ["vguiNPCShopSellFor"] = "Sell %s for %s",
    ["vguiNPCShopSetPrice"] = "Set price",
    ["vguiNPCShopResourcePrice"] = "Resource price",
    ["vguiNPCShopResourcePriceEntry"] = "How much should 1 of this resource cost?",
    ["vguiNPCShopExchange"] = "You exchanged %s %s for %s",

    ["vguiNPCHeader"] = "Crafting",

    ["vguiStorage"] = "Inventory",
    ["vguiStorageInfo"] = "Info",
    ["vguiStorageDrop"] = "How much %s would you like to drop?",
    ["vguiStorageDropResource"] = "Drop Resource",

    ["craftingTypeSWEP"] = "SWEP",
    ["craftingTypeSWEPWC"] = "Weapon Class",
    ["craftingTypeMoneyBag"] = "Money Bag",
    ["craftingTypeMinReward"] = "Minimum Reward",
    ["craftingTypeMaxReward"] = "Maximum Reward",
    ["craftingTypeReward"] = "You got %s from a money bag!",
    ["craftingTypeArmor"] = "Armor",
    ["craftingTypeMaxArmor"] = "Max Armor",
    ["craftingTypeHealth"] = "Health",
    ["craftingTypeMaxHealth"] = "Max Health",
    ["craftingTypeEntity"] = "Entity",
    ["craftingTypeEntityC"] = "Entity Class",
    ["craftingTypeResource"] = "Resource",
    ["craftingTypeResourceKey"] = "Resource Key",
    ["craftingTypeResourceAm"] = "Resource Amount",
    ["craftingTypeCraftable"] = "Craftable",
    ["craftingTypeCollectResources"] = "Collect resources",
    ["craftingTypeCraftItems"] = "Craft items",
    ["craftingTypeProp"] = "Prop",
    ["craftingTypeVehicle"] = "Vehicle",
    ["craftingTypeVehicleScript"] = "Vehicle Script",

    ["toolName"] = "Entity Placer",
    ["toolNoPermission"] = "You don't have permission to use this tool.",
    ["toolInvalidEnt"] = "Invalid Entity type, choose a valid one from the tool menu.",
    ["toolEntPlaced"] = "Entity succesfully placed.",
    ["toolEntRemoved"] = "Entity succesfully removed.",
    ["toolEntOnlyBCS"] = "You can only use this tool to remove/create an entity from BCS.",
    ["toolEntType"] = "Entity Type",
    ["toolInfo"] = "Places entity spawn positions for Brick's Crafting. LeftClick - Place. RightClick - Remove.",
}