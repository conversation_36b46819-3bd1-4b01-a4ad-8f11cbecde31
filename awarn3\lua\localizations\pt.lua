local language_code = "PT"

AWarn.Localization:RegisterLanguage( language_code, "Português" )

//Credit: https://www.gmodstore.com/users/76561198283808068

AWarn.Localization:AddDefinition( language_code, "welcome1", 					"Bem-vindo ao AWarn3!" )
AWarn.Localization:AddDefinition( language_code, "insufficientperms", 			"Permissões insuficientes para executar este comando." )
AWarn.Localization:AddDefinition( language_code, "insufficientperms2", 			"Permissões insuficientes para ver as advertências deste jogador." )
AWarn.Localization:AddDefinition( language_code, "commandnonexist", 			"Este comando não existe." )
AWarn.Localization:AddDefinition( language_code, "invalidtargetid", 			"Alvo ou ID inválido." )
AWarn.Localization:AddDefinition( language_code, "invalidtarget", 				"Alvo inválido." )
AWarn.Localization:AddDefinition( language_code, "reasonrequired", 				"É necessária uma Razão para Advertir" )
AWarn.Localization:AddDefinition( language_code, "remove1activewarn", 			"Você removeu 1 advertência ativa de" )
AWarn.Localization:AddDefinition( language_code, "deletedwarningid", 			"Advertência excluída - ID" )
AWarn.Localization:AddDefinition( language_code, "removeallwarnings", 			"Você removeu todas as advertências de" )
AWarn.Localization:AddDefinition( language_code, "deletedwarningsfor", 			"Foram excluídas todas as advertências de" )
AWarn.Localization:AddDefinition( language_code, "cantopenconsole", 			"Você não pode abrir o menu através do console do servidor." )
AWarn.Localization:AddDefinition( language_code, "invalidoption", 				"Opção Inválida." )
AWarn.Localization:AddDefinition( language_code, "invalidoptionvaluetype",		"O tipo de valor da opção é inválido." )
AWarn.Localization:AddDefinition( language_code, "optionsloaded",				"Opções Carregadas!" )
AWarn.Localization:AddDefinition( language_code, "nopunishment",				"Nenhuma punição para esta contagem de advertências." )
AWarn.Localization:AddDefinition( language_code, "punishmentsloaded",			"Punições Carregadas!" )
AWarn.Localization:AddDefinition( language_code, "playernotallowedwarn",		"Este jogador não pode ser advertido." )
AWarn.Localization:AddDefinition( language_code, "warnmessage1",				"Você foi advertido por %s: %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage2",				"Você advertiu %s: %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage3",				"%s foi advertido por %s: %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage4",				"Você foi advertido por %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage5",				"Você advertiu %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage6",				"%s foi advertido por %s." )
AWarn.Localization:AddDefinition( language_code, "joinmessage1",				"entrou no servidor com advertências." )
AWarn.Localization:AddDefinition( language_code, "joinmessage2",				"Sua última advertência foi em:" )
AWarn.Localization:AddDefinition( language_code, "joinmessage3",				"Bem-vindo de volta ao servidor! Parece que você já foi advertido no passado." )
AWarn.Localization:AddDefinition( language_code, "joinmessage4",				"Você pode ver as suas advertências a qualquer momento, digitando" )
AWarn.Localization:AddDefinition( language_code, "joinmessage5",				"O jogador está entrando com advertências ativas: " )
AWarn.Localization:AddDefinition( language_code, "closemenu",					"Fechar Menu" )
AWarn.Localization:AddDefinition( language_code, "searchplayers",				"Buscar Jogador" )
AWarn.Localization:AddDefinition( language_code, "viewwarnings",				"Advertências" )
AWarn.Localization:AddDefinition( language_code, "configuration",				"Configuração" )
AWarn.Localization:AddDefinition( language_code, "clientoptions",				"Opções do Usuário" )
AWarn.Localization:AddDefinition( language_code, "serveroptions",				"Opções do Servidor" )
AWarn.Localization:AddDefinition( language_code, "colorcustomization",			"Personalização de Cor" )
AWarn.Localization:AddDefinition( language_code, "colorselection",				"Seleção de Cor" )
AWarn.Localization:AddDefinition( language_code, "languageconfiguration",		"Personalização do Idioma" )
AWarn.Localization:AddDefinition( language_code, "selectlanguage",				"Selecione um Idioma" )
AWarn.Localization:AddDefinition( language_code, "enablekickpunish",			"Habilitar Punição de Kick" )
AWarn.Localization:AddDefinition( language_code, "enablebanpunish",				"Habilitar Punição de Ban" )
AWarn.Localization:AddDefinition( language_code, "enabledecay",					"Habilitar Decaimento de Adver. Ativa" )
AWarn.Localization:AddDefinition( language_code, "resetafterban",				"Resetar Adver. Ativa Após Banimento" )
AWarn.Localization:AddDefinition( language_code, "allowwarnadmins",				"Permitir Advertir Admins" )
AWarn.Localization:AddDefinition( language_code, "pressenter",					"Pressione Enter para Salvar Alteração" )
AWarn.Localization:AddDefinition( language_code, "entertosave",					"Enter para Salvar" )
AWarn.Localization:AddDefinition( language_code, "chatprefix",					"Prefixo do Chat" )
AWarn.Localization:AddDefinition( language_code, "warningdecayrate",			"Tempo de Decaimento (em minutos)" )
AWarn.Localization:AddDefinition( language_code, "serverlanguage",				"Idioma do Servidor" )
AWarn.Localization:AddDefinition( language_code, "punishmentsconfiguration",	"Configuração de Punição" )
AWarn.Localization:AddDefinition( language_code, "addpunishment",				"Adicionar Punição" )
AWarn.Localization:AddDefinition( language_code, "warnings",					"Advertências" )
AWarn.Localization:AddDefinition( language_code, "punishtype",					"Tipo de Punição" )
AWarn.Localization:AddDefinition( language_code, "punishlength",				"Duração da Punição" )
AWarn.Localization:AddDefinition( language_code, "playermessage",				"Mensagem do Jogador" )
AWarn.Localization:AddDefinition( language_code, "playername",					"Nome do Jogador" )
AWarn.Localization:AddDefinition( language_code, "messagetoplayer",				"Mensagem para o Jogador" )
AWarn.Localization:AddDefinition( language_code, "servermessage",				"Mensagem do Servidor" )
AWarn.Localization:AddDefinition( language_code, "messagetoserver",				"Mensagem para o Servidor" )
AWarn.Localization:AddDefinition( language_code, "deletewarning",				"Apagar Advertência" )
AWarn.Localization:AddDefinition( language_code, "punishaddmenu",				"Menu Adicionar Punição" )
AWarn.Localization:AddDefinition( language_code, "inminutes",					"Em Minutos" )
AWarn.Localization:AddDefinition( language_code, "0equalperma",					"0 = Permanente" )
AWarn.Localization:AddDefinition( language_code, "use%",						"Use %s para mostrar o nome do jogador" )
AWarn.Localization:AddDefinition( language_code, "setdefault",					"Definir Padrão" )
AWarn.Localization:AddDefinition( language_code, "showingownwarnings",			"Mostrar as suas próprias advertências" )
AWarn.Localization:AddDefinition( language_code, "warnedby",					"Advertido por" )
AWarn.Localization:AddDefinition( language_code, "warningserver",				"Advertência do Servidor" )
AWarn.Localization:AddDefinition( language_code, "warningreason",				"Razão da Advertência" )
AWarn.Localization:AddDefinition( language_code, "warningdate",					"Data da Advertência" )
AWarn.Localization:AddDefinition( language_code, "nothing",						"NADA" )
AWarn.Localization:AddDefinition( language_code, "submit",						"Enviar" )
AWarn.Localization:AddDefinition( language_code, "connectedplayers",			"Jogadores Conectados" )
AWarn.Localization:AddDefinition( language_code, "displaywarningsfor",			"Mostrando Advertências de" )
AWarn.Localization:AddDefinition( language_code, "activewarnings",				"Advertências Ativas" )
AWarn.Localization:AddDefinition( language_code, "selectedplayernowarnings",	"O jogador selecionado não tem advertências registradas." )
AWarn.Localization:AddDefinition( language_code, "selectplayerseewarnings",		"Selecione um jogador para ver as advertências do mesmo." )
AWarn.Localization:AddDefinition( language_code, "warnplayer",					"Advertir Jogador" )
AWarn.Localization:AddDefinition( language_code, "reduceactiveby1",				"Reduzir advertências ativas em 1" )
AWarn.Localization:AddDefinition( language_code, "playerwarningmenu",			"Menu de Advertência do Jogador" )
AWarn.Localization:AddDefinition( language_code, "playersearchmenu",			"Menu de Busca de Jogadores" )
AWarn.Localization:AddDefinition( language_code, "warningplayer",				"Advertindo Jogador" )
AWarn.Localization:AddDefinition( language_code, "excludeplayers",				"Excluir jogadores sem histórico de advertência" )
AWarn.Localization:AddDefinition( language_code, "searchforplayers",			"Buscar jogadores por nome ou SteamID64" )
AWarn.Localization:AddDefinition( language_code, "name",						"Nome" )
AWarn.Localization:AddDefinition( language_code, "lastplayed",					"Última Partida" )
AWarn.Localization:AddDefinition( language_code, "lastwarned",					"Última Advertência" )
AWarn.Localization:AddDefinition( language_code, "never",						"Nunca" )
AWarn.Localization:AddDefinition( language_code, "playerid",					"ID do Jogador" )
AWarn.Localization:AddDefinition( language_code, "lookupplayerwarnings",		"Pesquisar advertências deste jogador" )
AWarn.Localization:AddDefinition( language_code, "servername",					"Nome do Servidor" )
AWarn.Localization:AddDefinition( language_code, "clientjoinmessage",			"Exibir contagem de aviso para o jogador ao entrar" )
AWarn.Localization:AddDefinition( language_code, "adminjoinmessage",			"Exibir mensagem para administradores quando o jogador entrar com avisos" )
AWarn.Localization:AddDefinition( language_code, "punishmentoptions",			"Punições" )
AWarn.Localization:AddDefinition( language_code, "kickpunishdescription",		"Se habilitado, AWarn3 pode expulsar jogadores do servidor como punição." )
AWarn.Localization:AddDefinition( language_code, "banpunishdescription",		"Se habilitado, AWarn3 pode banir jogadores do servidor como punição." )
AWarn.Localization:AddDefinition( language_code, "enabledecaydescription",		"Se habilitado, os avisos ativos diminuirão com o tempo." )
AWarn.Localization:AddDefinition( language_code, "reasonrequireddescription",	"Se ativado, os administradores serão solicitados a fornecer um motivo em seu aviso." )
AWarn.Localization:AddDefinition( language_code, "resetafterbandescription",	"Se habilitado, os avisos ativos de um usuário serão redefinidos para 0 após serem banidos por AWarn3." )
AWarn.Localization:AddDefinition( language_code, "logevents",					"Registrar eventos de aviso." )
AWarn.Localization:AddDefinition( language_code, "logeventsdescription",		"Se ativado, as ações no AWarn3 serão registradas em um arquivo de texto." )
AWarn.Localization:AddDefinition( language_code, "allowwarnadminsdescription",	"Se habilitado, os administradores poderão avisar outros administradores." )
AWarn.Localization:AddDefinition( language_code, "clientjoinmessagedescription","Se ativado, os usuários que entrarem no servidor verão uma mensagem no bate-papo se tiverem avisos." )
AWarn.Localization:AddDefinition( language_code, "adminjoinmessagedescription",	"Se habilitado, os administradores do servidor verão quando algum jogador entrar com avisos." )
AWarn.Localization:AddDefinition( language_code, "chatprefixdescription",		"O comando chat usado para comandos AWarn3. Padrão: !warn" )
AWarn.Localization:AddDefinition( language_code, "warningdecayratedescription",	"O tempo (em minutos) que um jogador precisa estar conectado para que 1 aviso ativo diminua." )
AWarn.Localization:AddDefinition( language_code, "servernamedescription",		"O nome deste servidor. Isso é útil para várias configurações de servidor." )
AWarn.Localization:AddDefinition( language_code, "selectlanguagedescription",	"Este é o idioma no qual as mensagens do servidor serão exibidas." )
AWarn.Localization:AddDefinition( language_code, "theme",						"Tema da Interface" )
AWarn.Localization:AddDefinition( language_code, "themeselect",					"Selecione o tema" )
AWarn.Localization:AddDefinition( language_code, "punishgroup",					"Grupo de Punição" )
AWarn.Localization:AddDefinition( language_code, "grouptoset",					"Grupo para definir" )
AWarn.Localization:AddDefinition( language_code, "viewnotes",					"Ver notas do jogador" )
AWarn.Localization:AddDefinition( language_code, "playernotes",					"Notas do jogador" )
AWarn.Localization:AddDefinition( language_code, "interfacecustomizations",		"Customizações de Interface" )
AWarn.Localization:AddDefinition( language_code, "enableblur",					"Habilitar desfoque de fundo" )
AWarn.Localization:AddDefinition( language_code, "chooseapreset",				"Escolha uma predefinição (opcional)" )
AWarn.Localization:AddDefinition( language_code, "warningpresets",				"Presets" )
AWarn.Localization:AddDefinition( language_code, "addeditpreset",				"Adicionar/Editar Predefinição" )
AWarn.Localization:AddDefinition( language_code, "presetname",					"Nome Predefinido" )
AWarn.Localization:AddDefinition( language_code, "presetreason",				"Razão Predefinida" )
