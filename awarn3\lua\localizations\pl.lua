local language_code = "PL"

AWarn.Localization:RegisterLanguage( language_code, "Polish" )

//Credit: https://www.gmodstore.com/users/76561198086149246

AWarn.Localization:AddDefinition( language_code, "welcome1", 					"Witaj w AWarn3!" )
AWarn.Localization:AddDefinition( language_code, "insufficientperms", 			"Nie posiadasz uprawnienia, aby użyć tej komendy." )
AWarn.Localization:AddDefinition( language_code, "insufficientperms2", 			"Nie posiadasz uprawnienia, aby zobaczyć na ostrzeżenia tego gracza." )
AWarn.Localization:AddDefinition( language_code, "commandnonexist", 			"Ta komenda nie istnieje." )
AWarn.Localization:AddDefinition( language_code, "invalidtargetid", 			"Nieprawidłowy cel lub ID." )
AWarn.Localization:AddDefinition( language_code, "invalidtarget", 				"Nieprawidłowy cel." )
AWarn.Localization:AddDefinition( language_code, "reasonrequired", 				"Wymagany jest powód ostrzeżenia." )
AWarn.Localization:AddDefinition( language_code, "remove1activewarn", 			"Usunąłeś 1-no aktywne ostrzeżenie z" )
AWarn.Localization:AddDefinition( language_code, "deletedwarningid", 			"Usunięto ID ostrzeżenia" )
AWarn.Localization:AddDefinition( language_code, "removeallwarnings", 			"Usunąłeś wszelkie ostrzeżenia z gracza" )
AWarn.Localization:AddDefinition( language_code, "deletedwarningsfor", 			"Usunąłeś wszelkie ostrzeżenia z" )
AWarn.Localization:AddDefinition( language_code, "cantopenconsole", 			"Nie możesz otworzyć menu z konsoli serwerowej." )
AWarn.Localization:AddDefinition( language_code, "invalidoption", 				"Nieprawidłowa Opcja." )
AWarn.Localization:AddDefinition( language_code, "invalidoptionvaluetype",		"Nieprawidłowy Typ Wartości." )
AWarn.Localization:AddDefinition( language_code, "optionsloaded",				"Opcje zostały załadowane!" )
AWarn.Localization:AddDefinition( language_code, "nopunishment",				"Brak kary za taką ilość ostrzeżeń." )
AWarn.Localization:AddDefinition( language_code, "punishmentsloaded",			"Kary zostały załadowane!" )
AWarn.Localization:AddDefinition( language_code, "playernotallowedwarn",		"Ten gracz nie może zostać ostrzeżony." )
AWarn.Localization:AddDefinition( language_code, "warnmessage1",				"Zostałeś ostrzeżony przez gracza %s za %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage2",				"Nałożyłeś ostrzeżenia na gracza %s za %s" )
AWarn.Localization:AddDefinition( language_code, "warnmessage3",				"%s nałożył ostrzeżenie na %s za %s" )
AWarn.Localization:AddDefinition( language_code, "warnmessage4",				"Zostałeś ostrzeżony przez gracza %s" )
AWarn.Localization:AddDefinition( language_code, "warnmessage5",				"Nałożyłeś ostrzeżenia na gracza %s." )
AWarn.Localization:AddDefinition( language_code, "warnmessage6",				"%s nałożył ostrzeżenie na %s." )
AWarn.Localization:AddDefinition( language_code, "joinmessage1",				"połączył się z serwerem z ostrzeżeniami." )
AWarn.Localization:AddDefinition( language_code, "joinmessage2",				"Jego ostatnio ostrzeżenie zostało nałożone:" )
AWarn.Localization:AddDefinition( language_code, "joinmessage3",				"Witaj ponownie! Wygląda na to że już otrzymałeś ostrzeżenie w przeszłości." )
AWarn.Localization:AddDefinition( language_code, "joinmessage4",				"Możesz podejrzeć ilość ostrzeżeń za pomocą komendy" )
AWarn.Localization:AddDefinition( language_code, "closemenu",					"Zamknij Menu" )
AWarn.Localization:AddDefinition( language_code, "searchplayers",				"Szukaj Graczy" )
AWarn.Localization:AddDefinition( language_code, "viewwarnings",				"Pokaż Ostrzeżenia" )
AWarn.Localization:AddDefinition( language_code, "configuration",				"Konfiguracja" )
AWarn.Localization:AddDefinition( language_code, "clientoptions",				"Opcje Użytkownika" )
AWarn.Localization:AddDefinition( language_code, "serveroptions",				"Opcje Serwerowe" )
AWarn.Localization:AddDefinition( language_code, "colorcustomization",			"Dostosuj Kolor" )
AWarn.Localization:AddDefinition( language_code, "colorselection",				"Wybierz Kolor" )
AWarn.Localization:AddDefinition( language_code, "languageconfiguration",		"Opcje Językowe" )
AWarn.Localization:AddDefinition( language_code, "selectlanguage",				"Wybierz Język" )
AWarn.Localization:AddDefinition( language_code, "enablekickpunish",			"Włącz Opcję Kickowania(po osiągnięciu x liczby ostrzeżeń)" )
AWarn.Localization:AddDefinition( language_code, "enablebanpunish",				"Włącz Opcję Banowania(po osiągnięciu x liczby ostrzeżeń)" )
AWarn.Localization:AddDefinition( language_code, "enabledecay",					"Włącz Opcję Upływania Aktywnych Ostrzeżeń" )
AWarn.Localization:AddDefinition( language_code, "resetafterban",				"Zresetuj aktywne ostrzeżenia po otrzymaniu bana" )
AWarn.Localization:AddDefinition( language_code, "allowwarnadmins",				"Zezwól na ostrzeganie Administratorów" )
AWarn.Localization:AddDefinition( language_code, "pressenter",					"Wciśnij Enter, aby zapisać zmiany." )
AWarn.Localization:AddDefinition( language_code, "entertosave",					"Enter aby zapisać" )
AWarn.Localization:AddDefinition( language_code, "chatprefix",					"Przedrostek wyświetlany na chatcie" )
AWarn.Localization:AddDefinition( language_code, "warningdecayrate",			"Szybkość Przemijania Ostrzeżeń (w minutach)" )
AWarn.Localization:AddDefinition( language_code, "serverlanguage",				"Język Serwera" )
AWarn.Localization:AddDefinition( language_code, "punishmentsconfiguration",	"Konfiguracja Systemu Kar" )
AWarn.Localization:AddDefinition( language_code, "addpunishment",				"Dodaj Karę" )
AWarn.Localization:AddDefinition( language_code, "warnings",					"Ostrzeżenia" )
AWarn.Localization:AddDefinition( language_code, "punishtype",					"Typ Kary" )
AWarn.Localization:AddDefinition( language_code, "punishlength",				"Długość Kary" )
AWarn.Localization:AddDefinition( language_code, "playermessage",				"Wiadomość" )
AWarn.Localization:AddDefinition( language_code, "playername",					"Nazwa Gracza" )
AWarn.Localization:AddDefinition( language_code, "messagetoplayer",				"Message do Gracza" )
AWarn.Localization:AddDefinition( language_code, "servermessage",				"Wiadomość Serwerowa" )
AWarn.Localization:AddDefinition( language_code, "messagetoserver",				"Wiadomość do Serwera" )
AWarn.Localization:AddDefinition( language_code, "deletewarning",				"Usuń Ostrzeżenie" )
AWarn.Localization:AddDefinition( language_code, "punishaddmenu",				"Menu Dodawania Kary" )
AWarn.Localization:AddDefinition( language_code, "inminutes",					"W minutach" )
AWarn.Localization:AddDefinition( language_code, "0equalperma",					"0 = Permanentny" )
AWarn.Localization:AddDefinition( language_code, "use%",						"Użyj %s, aby wyświetlić nazwę gracza" )
AWarn.Localization:AddDefinition( language_code, "setdefault",					"Ustaw domyślne" )
AWarn.Localization:AddDefinition( language_code, "showingownwarnings",			"Pokazywanie Własnych Ostrzeżeń" )
AWarn.Localization:AddDefinition( language_code, "warnedby",					"Ostrzeżony przez" )
AWarn.Localization:AddDefinition( language_code, "warningserver",				"Serwer" )
AWarn.Localization:AddDefinition( language_code, "warningreason",				"Powód" )
AWarn.Localization:AddDefinition( language_code, "warningdate",					"Data" )
AWarn.Localization:AddDefinition( language_code, "nothing",						"NIC" )
AWarn.Localization:AddDefinition( language_code, "submit",						"Zatwierdź" )
AWarn.Localization:AddDefinition( language_code, "connectedplayers",			"Połączeni gracze" )
AWarn.Localization:AddDefinition( language_code, "displaywarningsfor",			"Wyświetlanie Ostrzeżeń dla" )
AWarn.Localization:AddDefinition( language_code, "activewarnings",				"Aktywne ostrzeżenia" )
AWarn.Localization:AddDefinition( language_code, "selectedplayernowarnings",	"Wybrany gracz nie został ostrzeżony." )
AWarn.Localization:AddDefinition( language_code, "selectplayerseewarnings",		"Wybierz gracza aby zobaczyć jego ostrzeżenia." )
AWarn.Localization:AddDefinition( language_code, "warnplayer",					"Ostrzeż Gracza" )
AWarn.Localization:AddDefinition( language_code, "reduceactiveby1",				"Zmniejsz liczbę aktywnych ostrzeżeń o 1" )
AWarn.Localization:AddDefinition( language_code, "playerwarningmenu",			"Menu Ostrzeżeń Gracza" )
AWarn.Localization:AddDefinition( language_code, "playersearchmenu",			"Menu Wyszukiwania Gracza" )
AWarn.Localization:AddDefinition( language_code, "warningplayer",				"Ostrzeganie Gracza" )
AWarn.Localization:AddDefinition( language_code, "excludeplayers",				"Wyklucz graczy, którzy nie mają żadnego ostrzeżenia" )
AWarn.Localization:AddDefinition( language_code, "searchforplayers",			"Szukaj gracza za pomocą nazwy lub SteamID64" )
AWarn.Localization:AddDefinition( language_code, "name",						"Nazwa" )
AWarn.Localization:AddDefinition( language_code, "lastplayed",					"Widziany Ostatnio" )
AWarn.Localization:AddDefinition( language_code, "lastwarned",					"Ostatnio Ostrzeżony" )
AWarn.Localization:AddDefinition( language_code, "never",						"Nigdy" )
AWarn.Localization:AddDefinition( language_code, "playerid",					"ID Gracza" )
AWarn.Localization:AddDefinition( language_code, "lookupplayerwarnings",		"Spójrz na ostrzeżenia tego gracza." )
AWarn.Localization:AddDefinition( language_code, "servername",					"Nazwa Serwera" )
AWarn.Localization:AddDefinition( language_code, "clientjoinmessage",			"Wyświetlaj liczbę ostrzeżeń dla gracza przy dołączaniu" )
AWarn.Localization:AddDefinition( language_code, "adminjoinmessage",			"Wyświetlaj wiadomość dla administratorów, gdy gracz dołącza z ostrzeżeniami" )
AWarn.Localization:AddDefinition( language_code, "punishmentoptions",			"Kary" )
AWarn.Localization:AddDefinition( language_code, "kickpunishdescription",		"Jeśli jest włączony, AWarn3 może wyrzucać graczy z serwera jako karę." )
AWarn.Localization:AddDefinition( language_code, "banpunishdescription",		"Jeśli jest włączony, AWarn3 może za karę zbanować graczy z serwera." )
AWarn.Localization:AddDefinition( language_code, "enabledecaydescription",		"Jeśli ta opcja jest włączona, aktywne ostrzeżenia będą z czasem zanikać." )
AWarn.Localization:AddDefinition( language_code, "reasonrequireddescription",	"Jeśli ta opcja jest włączona, administratorzy będą musieli podać powód w swoim ostrzeżeniu." )
AWarn.Localization:AddDefinition( language_code, "resetafterbandescription",	"Jeśli ta opcja jest włączona, aktywne ostrzeżenia użytkowników zostaną zresetowane do 0 po zbanowaniu ich przez AWarn3." )
AWarn.Localization:AddDefinition( language_code, "logevents",					"Rejestruj zdarzenia ostrzegawcze." )
AWarn.Localization:AddDefinition( language_code, "logeventsdescription",		"Jeśli ta opcja jest włączona, działania w AWarn3 będą rejestrowane w pliku tekstowym." )
AWarn.Localization:AddDefinition( language_code, "allowwarnadminsdescription",	"Jeśli ta opcja jest włączona, administratorzy będą mogli ostrzegać innych administratorów." )
AWarn.Localization:AddDefinition( language_code, "clientjoinmessagedescription","Jeśli ta opcja jest włączona, użytkownicy, którzy dołączą do serwera, zobaczą wiadomość na czacie, jeśli otrzymają ostrzeżenia." )
AWarn.Localization:AddDefinition( language_code, "adminjoinmessagedescription",	"Jeśli ta opcja jest włączona, administratorzy na serwerze zobaczą, kiedy dołączy dowolny gracz, który ma ostrzeżenia." )
AWarn.Localization:AddDefinition( language_code, "chatprefixdescription",		"Polecenie chat używane w poleceniach AWarn3. Domyślnie: !warn" )
AWarn.Localization:AddDefinition( language_code, "warningdecayratedescription",	"Czas (w minutach), przez który gracz musi być połączony, aby 1 aktywne ostrzeżenie zanikło." )
AWarn.Localization:AddDefinition( language_code, "servernamedescription",		"Nazwa tego serwera. Jest to przydatne w przypadku konfiguracji z wieloma serwerami." )
AWarn.Localization:AddDefinition( language_code, "selectlanguagedescription",	"To jest język, w którym będą wyświetlane komunikaty serwera." )
AWarn.Localization:AddDefinition( language_code, "theme",						"Motyw interfejsu" )
AWarn.Localization:AddDefinition( language_code, "themeselect",					"Wybierz motyw" )
AWarn.Localization:AddDefinition( language_code, "punishgroup",					"Grupa karania" )
AWarn.Localization:AddDefinition( language_code, "grouptoset",					"Grupuj do ustawienia" )
AWarn.Localization:AddDefinition( language_code, "viewnotes",					"Wyświetl notatki gracza" )
AWarn.Localization:AddDefinition( language_code, "playernotes",					"Notatki gracza" )
AWarn.Localization:AddDefinition( language_code, "interfacecustomizations",		"Dostosowywanie interfejsu" )
AWarn.Localization:AddDefinition( language_code, "enableblur",					"Włącz rozmycie tła" )
AWarn.Localization:AddDefinition( language_code, "chooseapreset",				"Wybierz ustawienie (opcjonalnie)" )
AWarn.Localization:AddDefinition( language_code, "warningpresets",				"Presety" )
AWarn.Localization:AddDefinition( language_code, "addeditpreset",				"Dodaj/edytuj ustawienie wstępne" )
AWarn.Localization:AddDefinition( language_code, "presetname",					"Zaprogramowana nazwa" )
AWarn.Localization:AddDefinition( language_code, "presetreason",				"Wstępnie ustawiony powód" )