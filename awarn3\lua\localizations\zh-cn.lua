local language_code = "zh-CN"

--[[Chinese translation by 老子名叫李*天]]--
--https://www.gmodstore.com/users/76561198101134284

AWarn.Localization:RegisterLanguage( language_code, "Simplified Chinese" )

AWarn.Localization:AddDefinition( language_code, "welcome1", 					"欢迎来到AWarn3!" )
AWarn.Localization:AddDefinition( language_code, "insufficientperms", 			"没有足够权限" )
AWarn.Localization:AddDefinition( language_code, "insufficientperms2", 			"没有足够权限查看这个玩家的警告" )
AWarn.Localization:AddDefinition( language_code, "commandnonexist", 			"这个指令不存在" )
AWarn.Localization:AddDefinition( language_code, "invalidtargetid", 			"无效的对象或ID" )
AWarn.Localization:AddDefinition( language_code, "invalidtarget", 				"无效的对象" )
AWarn.Localization:AddDefinition( language_code, "reasonrequired", 				"需要警告理由" )
AWarn.Localization:AddDefinition( language_code, "remove1activewarn", 			"你从中删除了一个警告" )
AWarn.Localization:AddDefinition( language_code, "deletedwarningid", 			"删除警告ID" )
AWarn.Localization:AddDefinition( language_code, "removeallwarnings", 			"你从中移除了所有警告" )
AWarn.Localization:AddDefinition( language_code, "deletedwarningsfor", 			"为...删除所有警告" )
AWarn.Localization:AddDefinition( language_code, "cantopenconsole", 			"你不能从服务器控制台打开这个目录." )
AWarn.Localization:AddDefinition( language_code, "invalidoption", 				"无效设置." )
AWarn.Localization:AddDefinition( language_code, "invalidoptionvaluetype",		"无效的设置类型" )
AWarn.Localization:AddDefinition( language_code, "optionsloaded",				"配置加载完毕" )
AWarn.Localization:AddDefinition( language_code, "nopunishment",				"对此警告不做惩罚." )
AWarn.Localization:AddDefinition( language_code, "punishmentsloaded",			"惩罚已加载完毕" )
AWarn.Localization:AddDefinition( language_code, "playernotallowedwarn",		"这位玩家不允许被警告" )
AWarn.Localization:AddDefinition( language_code, "warnmessage1",				"你因 %s 被 %s 警告." )
AWarn.Localization:AddDefinition( language_code, "warnmessage2",				"你因 %s 被 %s 警告" )
AWarn.Localization:AddDefinition( language_code, "warnmessage3",				"%s 被 %s 警告因为 %s" )
AWarn.Localization:AddDefinition( language_code, "warnmessage4",				"你因 %s 被." )
AWarn.Localization:AddDefinition( language_code, "warnmessage5",				"你因 %s 被." )
AWarn.Localization:AddDefinition( language_code, "warnmessage6",				"%s 被 %s 警告" )
AWarn.Localization:AddDefinition( language_code, "joinmessage1",				"某人带着警告进入服务器" )
AWarn.Localization:AddDefinition( language_code, "joinmessage2",				"他们的最后一条警告是" )
AWarn.Localization:AddDefinition( language_code, "joinmessage3",				"欢迎回到服务器! 这个显示了你上次的警告" )
AWarn.Localization:AddDefinition( language_code, "joinmessage4",				"你可以随时在聊天栏查看你的警告" )
AWarn.Localization:AddDefinition( language_code, "closemenu",					"关闭目录" )
AWarn.Localization:AddDefinition( language_code, "searchplayers",				"搜索玩家" )
AWarn.Localization:AddDefinition( language_code, "viewwarnings",				"查看警告" )
AWarn.Localization:AddDefinition( language_code, "configuration",				"配置" )
AWarn.Localization:AddDefinition( language_code, "clientoptions",				"用户设置" )
AWarn.Localization:AddDefinition( language_code, "serveroptions",				"服务器设置" )
AWarn.Localization:AddDefinition( language_code, "colorcustomization",			"颜色定制" )
AWarn.Localization:AddDefinition( language_code, "colorselection",				"颜色选择" )
AWarn.Localization:AddDefinition( language_code, "languageconfiguration",		"自定义语言" )
AWarn.Localization:AddDefinition( language_code, "selectlanguage",				"选择一个语言" )
AWarn.Localization:AddDefinition( language_code, "enablekickpunish",			"启用kick处罚" )
AWarn.Localization:AddDefinition( language_code, "enablebanpunish",				"启用ban处罚" )
AWarn.Localization:AddDefinition( language_code, "enabledecay",					"启用警告消退时间" )
AWarn.Localization:AddDefinition( language_code, "resetafterban",				"在ban后重设警告" )
AWarn.Localization:AddDefinition( language_code, "allowwarnadmins",				"允许警告管理" )
AWarn.Localization:AddDefinition( language_code, "pressenter",					"按下enter保存改变" )
AWarn.Localization:AddDefinition( language_code, "entertosave",					"按enter" )
AWarn.Localization:AddDefinition( language_code, "chatprefix",					"聊天前缀" )
AWarn.Localization:AddDefinition( language_code, "warningdecayrate",			"警告消退时间 (分钟)" )
AWarn.Localization:AddDefinition( language_code, "serverlanguage",				"服务器语言" )
AWarn.Localization:AddDefinition( language_code, "punishmentsconfiguration",	"惩罚配置" )
AWarn.Localization:AddDefinition( language_code, "addpunishment",				"添加一个惩罚配置" )
AWarn.Localization:AddDefinition( language_code, "warnings",					"警告" )
AWarn.Localization:AddDefinition( language_code, "punishtype",					"惩罚类型" )
AWarn.Localization:AddDefinition( language_code, "punishlength",				"惩罚时间" )
AWarn.Localization:AddDefinition( language_code, "playermessage",				"玩家信息" )
AWarn.Localization:AddDefinition( language_code, "playername",					"玩家姓名" )
AWarn.Localization:AddDefinition( language_code, "messagetoplayer",				"给玩家发信息" )
AWarn.Localization:AddDefinition( language_code, "servermessage",				"服务器信息" )
AWarn.Localization:AddDefinition( language_code, "messagetoserver",				"给服务器发信息" )
AWarn.Localization:AddDefinition( language_code, "deletewarning",				"删除警告" )
AWarn.Localization:AddDefinition( language_code, "punishaddmenu",				"惩罚加入菜单" )
AWarn.Localization:AddDefinition( language_code, "inminutes",					"几分钟后" )
AWarn.Localization:AddDefinition( language_code, "0equalperma",					"0 = 永久" )
AWarn.Localization:AddDefinition( language_code, "use%",						"使用 %s 展示玩家的名称" )
AWarn.Localization:AddDefinition( language_code, "setdefault",					"默认设置" )
AWarn.Localization:AddDefinition( language_code, "showingownwarnings",			"显示你自己的警告" )
AWarn.Localization:AddDefinition( language_code, "warnedby",					"被警告" )
AWarn.Localization:AddDefinition( language_code, "warningserver",				"警告的服务器" )
AWarn.Localization:AddDefinition( language_code, "warningreason",				"警告理由" )
AWarn.Localization:AddDefinition( language_code, "warningdate",					"警告日期" )
AWarn.Localization:AddDefinition( language_code, "nothing",						"什么都没有" )
AWarn.Localization:AddDefinition( language_code, "submit",						"提交" )
AWarn.Localization:AddDefinition( language_code, "connectedplayers",			"已经连接服务器的玩家" )
AWarn.Localization:AddDefinition( language_code, "displaywarningsfor",			"显示警告" )
AWarn.Localization:AddDefinition( language_code, "activewarnings",				"启用警告" )
AWarn.Localization:AddDefinition( language_code, "selectedplayernowarnings",	"选出在记录上没有警告的玩家." )
AWarn.Localization:AddDefinition( language_code, "selectplayerseewarnings",		"选择一个玩家以查看其警告" )
AWarn.Localization:AddDefinition( language_code, "warnplayer",					"警告玩家" )
AWarn.Localization:AddDefinition( language_code, "reduceactiveby1",				"减少启用中的警告数量至1个" )
AWarn.Localization:AddDefinition( language_code, "playerwarningmenu",			"玩家警告目录" )
AWarn.Localization:AddDefinition( language_code, "playersearchmenu",			"玩家搜索目录" )
AWarn.Localization:AddDefinition( language_code, "warningplayer",				"警告中的玩家" )
AWarn.Localization:AddDefinition( language_code, "excludeplayers",				"包括没有警告历史的玩家" )
AWarn.Localization:AddDefinition( language_code, "searchforplayers",			"通过玩家名称或者steam64位ID搜索玩家" )
AWarn.Localization:AddDefinition( language_code, "name",						"名称" )
AWarn.Localization:AddDefinition( language_code, "lastplayed",					"最后游玩" )
AWarn.Localization:AddDefinition( language_code, "lastwarned",					"最后的警告" )
AWarn.Localization:AddDefinition( language_code, "never",						"绝不" )
AWarn.Localization:AddDefinition( language_code, "playerid",					"玩家 ID" )
AWarn.Localization:AddDefinition( language_code, "lookupplayerwarnings",		"查阅这位玩家的警告" )
AWarn.Localization:AddDefinition( language_code, "servername",					"服务器名称" )
AWarn.Localization:AddDefinition( language_code, "clientjoinmessage",			"加入时向玩家显示警告计数" )
AWarn.Localization:AddDefinition( language_code, "adminjoinmessage",			"当玩家加入警告时向管理员显示消息" )
AWarn.Localization:AddDefinition( language_code, "punishmentoptions",			"处罚" )
AWarn.Localization:AddDefinition( language_code, "kickpunishdescription",		"如果启用，作为惩罚，AWarn3 可以将玩家踢出服务器。" )
AWarn.Localization:AddDefinition( language_code, "banpunishdescription",		"如果启用，作为惩罚，AWarn3 可以禁止玩家进入服务器。" )
AWarn.Localization:AddDefinition( language_code, "enabledecaydescription",		"如果启用，活动警告将随着时间的推移而衰减。" )
AWarn.Localization:AddDefinition( language_code, "reasonrequireddescription",	"如果启用，管理员将需要在警告中提供原因。" )
AWarn.Localization:AddDefinition( language_code, "resetafterbandescription",	"如果启用，用户的活动警告在被 AWarn3 禁止后将重置为 0。" )
AWarn.Localization:AddDefinition( language_code, "logevents",					"记录警告事件。" )
AWarn.Localization:AddDefinition( language_code, "logeventsdescription",		"如果启用，则 AWarn3 中的操作将被记录到文本文件中。" )
AWarn.Localization:AddDefinition( language_code, "allowwarnadminsdescription",	"如果启用，管理员将能够警告其他管理员。" )
AWarn.Localization:AddDefinition( language_code, "clientjoinmessagedescription","如果启用，加入服务器的用户将在收到警告时在聊天中看到一条消息。" )
AWarn.Localization:AddDefinition( language_code, "adminjoinmessagedescription",	"如果启用，服务器上的管理员将看到任何有警告的玩家加入。" )
AWarn.Localization:AddDefinition( language_code, "chatprefixdescription",		"用于 AWarn3 命令的 chat 命令。 默认值： ！warn" )
AWarn.Localization:AddDefinition( language_code, "warningdecayratedescription",	"玩家需要连接的时间（以分钟为单位）才能使 1 个主动警告衰减。" )
AWarn.Localization:AddDefinition( language_code, "servernamedescription",		"此服务器的名称。 这对于多服务器设置很有用。" )
AWarn.Localization:AddDefinition( language_code, "selectlanguagedescription",	"这是服务器消息将使用的语言。" )
AWarn.Localization:AddDefinition( language_code, "theme",						"界面主题" )
AWarn.Localization:AddDefinition( language_code, "themeselect",					"选择主题" )
AWarn.Localization:AddDefinition( language_code, "punishgroup",					"惩戒组" )
AWarn.Localization:AddDefinition( language_code, "grouptoset",					"要设置的组" )
AWarn.Localization:AddDefinition( language_code, "viewnotes",					"查看玩家笔记" )
AWarn.Localization:AddDefinition( language_code, "playernotes",					"玩家笔记" )
AWarn.Localization:AddDefinition( language_code, "interfacecustomizations",		"界面定制" )
AWarn.Localization:AddDefinition( language_code, "enableblur",					"启用背景模糊" )
AWarn.Localization:AddDefinition( language_code, "chooseapreset",				"选择预设（可选）" )
AWarn.Localization:AddDefinition( language_code, "warningpresets",				"预设" )
AWarn.Localization:AddDefinition( language_code, "addeditpreset",				"添加/编辑预设" )
AWarn.Localization:AddDefinition( language_code, "presetname",					"预设名称" )
AWarn.Localization:AddDefinition( language_code, "presetreason",				"预设原因" )
