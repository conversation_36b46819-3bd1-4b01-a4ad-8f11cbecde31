local JUTSU = {}

JUTSU.Config = {
    ["tC<PERSON>ra"] =     {
        [1] = 1,
        [2] = 1,
        [3] = 1,
        [4] = 1,
        [5] = 1,
      },
    ["tCooldown"] =     {
        [1] = 100,
        [2] = 100,
        [3] = 100,
        [4] = 100,
        [5] = 100,
      },
    ["tSpeedBoost"] =     {
        [1] = 10,
        [2] = 10,
        [3] = 10,
        [4] = 10,
        [5] = 10,
      },
    ["tForceBoost"] =     {
        [1] = 10,
        [2] = 10,
        [3] = 10,
        [4] = 10,
        [5] = 10,
      },
    ["tResistance"] =     {
        [1] = 10,
        [2] = 10,
        [3] = 10,
        [4] = 10,
        [5] = 10,
      },
    ["tHealthRemove"] =     {
        [1] = 4,
        [2] = 4,
        [3] = 4,
        [4] = 4,
        [5] = 4,
      },
  }

-- function JUTSU:Call(pOwner, iLevel)



    
--     timer.Simple(5, function()
--         if IsValid(eEntites) then
--             eEntites:Remove()
--         end
--     end)

-- end

function JUTSU:Call(pOwner, iLevel)
    --pOwner:PlaySealSound(4, .8)
    local timerName = "ATG:YOLTIX:MarqueMaudite1" .. pOwner:SteamID64()
    local enabled = pOwner.MarqueMaudite1 == nil and false or pOwner.MarqueMaudite1



    if not enabled then

		pOwner:EmitSound("geams/solve_jutsu/solve_marque_maudite.wav")

        
        local headbone = pOwner:LookupBone("ValveBiped.Bip01_Head1")
        
        local eMark = ents.Create("prop_physics")
        eMark:SetModel("models/geams_face_maudit/face_maudit_01.mdl")
        eMark:SetMoveType(MOVETYPE_NONE)
        eMark:SetSolid(SOLID_NONE)
        eMark:SetCollisionGroup(COLLISION_GROUP_WORLD)
        eMark:Spawn()
        eMark:FollowBone(pOwner, headbone)
        eMark:SetLocalPos(Vector(3.2, -0.3, -0.9))
        eMark:SetLocalAngles(Angle(77.6, 0, -44.5))

        local phys = eMark:GetPhysicsObject()
        if IsValid(phys) then
            phys:EnableCollisions(false)
            phys:Sleep()
        end

        -- Création du props au sol (signe maudit)
        local eGroundSign = ents.Create("prop_physics")
        eGroundSign:SetModel("models/geams_naruto/jutsu/sign_maudit01.mdl")
        eGroundSign:SetMoveType(MOVETYPE_NONE)
        eGroundSign:SetSolid(SOLID_NONE)
        eGroundSign:SetCollisionGroup(COLLISION_GROUP_WORLD)

        -- Position aux pieds du joueur
        local playerPos = pOwner:GetPos()
        eGroundSign:SetPos(playerPos)
        eGroundSign:SetAngles(Angle(0, pOwner:GetAngles().y, 0))
        eGroundSign:Spawn()

        local physGround = eGroundSign:GetPhysicsObject()
        if IsValid(physGround) then
            physGround:EnableCollisions(false)
            physGround:Sleep()
        end

        local eEntites = DATG:DoorTaijutsu(pOwner, "solve_marque_maudite_02")

        pOwner.MarqueMaudite1 = eEntites
        pOwner.MarqueEntites = eMark
        pOwner.MarqueGroundSign = eGroundSign

        pOwner:ActiveBoostForce(self.Config.tForceBoost[iLevel])
        pOwner:ActiveBoostVitesse(self.Config.tSpeedBoost[iLevel])
        pOwner:ActiveBoostResistance(self.Config.tResistance[iLevel])

        util.ScreenShake( pOwner:GetPos(), 10, 10, 2.5, 300, true )

        timer.Create(timerName, 1, 0, function()
            if not pOwner.MarqueMaudite1 or not pOwner.MarqueEntites or not pOwner.MarqueGroundSign then
                timer.Remove(timerName)
            end

            if pOwner:Health() - self.Config.tHealthRemove[iLevel] <= 0 then

                pOwner:RemoveSpeedVitesse(self.Config.tSpeedBoost[iLevel])
                pOwner:RemoveBoostForce(self.Config.tForceBoost[iLevel])
                pOwner:RemoveBoostResistance(self.Config.tResistance[iLevel])


                local entites = pOwner.MarqueMaudite1
                if IsValid(entites) then
                    entites:Remove()
                end
                pOwner.MarqueMaudite1 = false;


                local entites = pOwner.MarqueEntites
                if IsValid(entites) then
                    entites:Remove()
                end
                pOwner.MarqueEntites = false;

                local groundSign = pOwner.MarqueGroundSign
                if IsValid(groundSign) then
                    groundSign:Remove()
                end
                pOwner.MarqueGroundSign = false;

                timer.Remove(timerName)
                return
            end

            pOwner:SetHealth(pOwner:Health() - self.Config.tHealthRemove[iLevel])
        end)

        return true
    else
        local entites = pOwner.MarqueMaudite1
        if IsValid(entites) then
            entites:Remove()
        end

        pOwner.MarqueMaudite1 = false;

        local entites = pOwner.MarqueEntites
        if IsValid(entites) then
            entites:Remove()
        end
        pOwner.MarqueEntites = false;

        local groundSign = pOwner.MarqueGroundSign
        if IsValid(groundSign) then
            groundSign:Remove()
        end
        pOwner.MarqueGroundSign = false;

        pOwner:RemoveSpeedVitesse(self.Config.tSpeedBoost[iLevel])
        pOwner:RemoveBoostForce(self.Config.tForceBoost[iLevel])
        pOwner:RemoveBoostResistance(self.Config.tResistance[iLevel])

        timer.Remove(timerName)
        return false
    end
end

hook.Add("PlayerDeath", "ATG:YOLTIX:MarqueMaudite1:PlayerDeath", function(pVictim, tInflictor, pAttacker)
    local bEnable = pVictim.MarqueMaudite1
    if not bEnable then return end

    local entites = pVictim.MarqueMaudite1
    if IsValid(entites) then
        entites:Remove()
    end

    pVictim.MarqueMaudite1 = false;

    local entites = pVictim.MarqueEntites
    if IsValid(entites) then
        entites:Remove()
    end
    pVictim.MarqueEntites = false;

    local groundSign = pVictim.MarqueGroundSign
    if IsValid(groundSign) then
        groundSign:Remove()
    end
    pVictim.MarqueGroundSign = false;
end)



local SteamIDWL = { ----------------- Steamid64
	["76561199120471532"] = true, -- Remplacez par le SteamID de la personne à qui vous voulez donner le jutsu
    ["76561198267184628"] = true, -- Remplacez par le SteamID de la personne à qui vous voulez donner le jutsu
    ["76561199213701800"] = true, -- Remplacez par le SteamID de la personne à qui vous voulez donner le jutsu
}

function JUTSU.CustomUnlock(pOwner, iLevel)
	if not IsValid(pOwner) then return end

	if SteamIDWL[pOwner:SteamID64()] then
		return true
	else
		return false
	end
end



DATG:RegisterJutsu(194, JUTSU) 